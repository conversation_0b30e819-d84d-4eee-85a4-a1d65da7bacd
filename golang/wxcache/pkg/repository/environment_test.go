package repository_test

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
	"llif.org/wxcache/internal/convertor"
	"llif.org/wxcache/pkg/testutil/location"
	"llif.org/wxcache/pkg/testutil/testsetup"
	"llif.org/wxcache/pkg/wxtypes"
)

func TestEnvironmentRepositoryCanFetchDataIntegration(t *testing.T) {
	if testing.Short() {
		t.Skip("skipping integration test")
	}

	c, err := testsetup.NewConfig()
	require.NoError(t, err)

	var (
		ctx      = context.Background()
		timeNow  = time.Now().UTC()
		lat, lon = location.Seoul.Lat, location.Seoul.Long

		aq = wxtypes.AirQualityV2{
			Timestamp: timeNow,
			Pollutants: wxtypes.AirQualityPollutants{
				PM10: convertor.FloatToFloatPtr(10.0),
			},
			AQI: wxtypes.AirQualityIndex{
				EU: convertor.IntToIntPtr(10),
			},
			Coordinates: wxtypes.Coordinates{
				Latitude:  lat,
				Longitude: lon,
			},
			Metadata: wxtypes.Metadata{
				Provider: "test",
			},
			SystemProperties: wxtypes.SystemProperties{
				CreatedAt: timeNow,
			},
		}

		w = wxtypes.WeatherV2{
			Timestamp: timeNow,
			Wind: wxtypes.WeatherWind{
				Speed: convertor.FloatToFloatPtr(10.0),
			},
			Temperature: wxtypes.WeatherTemperature{
				Temperature: convertor.FloatToFloatPtr(10),
			},
			Coordinates: wxtypes.Coordinates{
				Latitude:  lat,
				Longitude: lon,
			},
			Metadata: wxtypes.Metadata{
				Provider: "test",
			},
			SystemProperties: wxtypes.SystemProperties{
				CreatedAt: timeNow,
			},
		}

		p = wxtypes.PollenV2{
			Timestamp: timeNow,
			Tree: &wxtypes.PollenSpecies{
				Count: 10,
			},
			Coordinates: wxtypes.Coordinates{
				Latitude:  lat,
				Longitude: lon,
			},
			Metadata: wxtypes.Metadata{
				Provider: "test",
			},
			SystemProperties: wxtypes.SystemProperties{
				CreatedAt: timeNow,
			},
		}

		st = wxtypes.SpaceTime{
			Lat:      lat,
			Long:     lon,
			TimeFrom: timeNow,
			TimeTo:   timeNow.Add(1 * time.Hour),
		}
	)

	t.Run("Air Quality", func(t *testing.T) {
		// Insert
		err = c.Repository.AirQuality.Insert(ctx, []wxtypes.AirQualityV2{aq})
		require.NoError(t, err)

		// Fetch
		data, err := c.Repository.AirQuality.Fetch(ctx, st)
		require.NoError(t, err)
		require.NotEmpty(t, data)

		// Delete
		require.NoError(t, c.Repository.AirQuality.Delete(ctx, st))
	})

	t.Run("Weather", func(t *testing.T) {
		// Insert
		err = c.Repository.Weather.Insert(ctx, []wxtypes.WeatherV2{w})
		require.NoError(t, err)

		// Fetch
		data, err := c.Repository.Weather.Fetch(ctx, st)
		require.NoError(t, err)
		require.NotEmpty(t, data)

		// Delete
		require.NoError(t, c.Repository.Weather.Delete(ctx, st))
	})

	t.Run("Pollen", func(t *testing.T) {
		// Insert
		err = c.Repository.Pollen.Insert(ctx, []wxtypes.PollenV2{p})
		require.NoError(t, err)

		// Fetch
		data, err := c.Repository.Pollen.Fetch(ctx, st)
		require.NoError(t, err)
		require.NotEmpty(t, data)

		// Delete
		require.NoError(t, c.Repository.Pollen.Delete(ctx, st))
	})

}

func TestEnvironmentRepositoryCanFetchWithEmptyBucketsIncluded(t *testing.T) {
	if testing.Short() {
		t.Skip("skipping integration test")
	}

	c, err := testsetup.NewConfig()
	require.NoError(t, err)

	var (
		ctx        = context.Background()
		timestamps = []time.Time{
			time.Now().UTC().Add(-time.Hour),
			time.Now().UTC(),
			time.Now().UTC().Add(2 * time.Hour), // 1 hour gap
			time.Now().UTC().Add(3 * time.Hour),
		}

		lat, lon = location.Seoul.Lat, location.Seoul.Long

		aq = make([]wxtypes.AirQualityV2, 0)
		w  = make([]wxtypes.WeatherV2, 0)
		p  = make([]wxtypes.PollenV2, 0)
	)

	for _, t := range timestamps {
		aq = append(aq, wxtypes.AirQualityV2{
			Timestamp: t,
			Pollutants: wxtypes.AirQualityPollutants{
				PM10: convertor.FloatToFloatPtr(10.0),
			},
			AQI: wxtypes.AirQualityIndex{
				EU: convertor.IntToIntPtr(10),
			},
			Coordinates: wxtypes.Coordinates{
				Latitude:  lat,
				Longitude: lon,
			},
			Metadata: wxtypes.Metadata{
				Provider: "test",
			},
			SystemProperties: wxtypes.SystemProperties{
				CreatedAt: time.Now().UTC(),
			},
		})

		w = append(w, wxtypes.WeatherV2{
			Timestamp: t,
			Wind: wxtypes.WeatherWind{
				Speed: convertor.FloatToFloatPtr(10),
			},
			Temperature: wxtypes.WeatherTemperature{
				Temperature: convertor.FloatToFloatPtr(10),
			},
			Coordinates: wxtypes.Coordinates{
				Latitude:  lat,
				Longitude: lon,
			},
			Metadata: wxtypes.Metadata{
				Provider: "test",
			},
			SystemProperties: wxtypes.SystemProperties{
				CreatedAt: time.Now().UTC(),
			},
		})

		p = append(p, wxtypes.PollenV2{
			Timestamp: t,
			Weed: &wxtypes.PollenSpecies{
				Count: 10,
			},
			Coordinates: wxtypes.Coordinates{
				Latitude:  lat,
				Longitude: lon,
			},
			Metadata: wxtypes.Metadata{
				Provider: "test",
			},
			SystemProperties: wxtypes.SystemProperties{
				CreatedAt: time.Now().UTC(),
			},
		})
	}

	t.Run("Air Quality", func(t *testing.T) {
		// Insert
		err = c.Repository.AirQuality.Insert(ctx, aq)
		require.NoError(t, err)

		// Fetch
		data, emptyBuckets, err := c.Repository.AirQuality.FetchWithEmptyBuckets(ctx, wxtypes.SpaceTime{
			TimeFrom: timestamps[0],
			TimeTo:   timestamps[len(timestamps)-1],
			Lat:      lat,
			Long:     lon,
		})
		require.NoError(t, err)
		require.NotEmpty(t, emptyBuckets)
		require.NotEmpty(t, data)

		// Delete
		for _, ts := range timestamps {
			require.NoError(t, c.Repository.AirQuality.Delete(ctx, wxtypes.SpaceTime{
				Lat:      lat,
				Long:     lon,
				TimeFrom: ts,
				TimeTo:   ts,
			}))
		}
	})

	t.Run("Weather", func(t *testing.T) {
		// Insert
		err = c.Repository.Weather.Insert(ctx, w)
		require.NoError(t, err)

		// Fetch
		data, emptyBuckets, err := c.Repository.Weather.FetchWithEmptyBuckets(ctx, wxtypes.SpaceTime{
			TimeFrom: timestamps[0],
			TimeTo:   timestamps[len(timestamps)-1],
			Lat:      lat,
			Long:     lon,
		})
		require.NoError(t, err)
		require.NotEmpty(t, emptyBuckets)
		require.NotEmpty(t, data)

		// Delete
		for _, ts := range timestamps {
			require.NoError(t, c.Repository.Weather.Delete(ctx, wxtypes.SpaceTime{
				Lat:      lat,
				Long:     lon,
				TimeFrom: ts,
				TimeTo:   ts,
			}))
		}
	})

	t.Run("Pollen", func(t *testing.T) {
		// Insert
		err = c.Repository.Pollen.Insert(ctx, p)
		require.NoError(t, err)

		// Fetch
		data, emptyBuckets, err := c.Repository.Pollen.FetchWithEmptyBuckets(ctx, wxtypes.SpaceTime{
			TimeFrom: timestamps[0],
			TimeTo:   timestamps[len(timestamps)-1],
			Lat:      lat,
			Long:     lon,
		})
		require.NoError(t, err)
		require.NotEmpty(t, emptyBuckets)
		require.NotEmpty(t, data)

		// Delete
		for _, ts := range timestamps {
			require.NoError(t, c.Repository.Pollen.Delete(ctx, wxtypes.SpaceTime{
				Lat:      lat,
				Long:     lon,
				TimeFrom: ts,
				TimeTo:   ts,
			}))
		}
	})
}
