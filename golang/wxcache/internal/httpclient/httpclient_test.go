package httpclient

import (
	"net/http"
	"net/url"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
	"golang.org/x/time/rate"
)

func TestHttpClientNoRateLimitShouldPass(t *testing.T) {
	requestsPerSecond := 10
	client := getHttpClientAndLimiter(requestsPerSecond)
	request := getTestHttpRequest()

	_, err := client.Do(request)
	require.NoError(t, err)
}

func TestHttpClientRaisesRateLimitExceeded(t *testing.T) {
	requestsPerSecond := 0

	client := getHttpClientAndLimiter(requestsPerSecond)
	request := getTestHttpRequest()

	_, err := client.Do(request)
	if err == nil {
		t.Fatal("request, which was supposed to be rate limited, did not return an error")
	}

	if err != nil {
		if _, ok := err.(*RateLimitExceeded); !ok {
			t.Fatalf("http client was not rate limited and instead got err=%s", err)
		}
	}
}

func TestHttpClientRaisesRateLimitExceededAfterReachingLimit(t *testing.T) {
	requestsPerSecond := 10

	client := getHttpClientAndLimiter(requestsPerSecond)
	request := getTestHttpRequest()

	// Exhaust limiter's tokens
	client.limiter.AllowN(time.Now(), requestsPerSecond)

	// Assert the request fails on the next try
	_, err := client.Do(request)
	require.Error(t, err)

	// Make sure the returned error is the rate limit
	if _, ok := err.(*RateLimitExceeded); !ok {
		t.Fatalf("http client was not rate limited and instead got err=%s", err)
	}
}

func TestHttpClientExhaustsRateLimitButThenWaitsToSendSuccessfulRequest(t *testing.T) {
	requestsPerSecond := 10

	client := getHttpClientAndLimiter(requestsPerSecond)
	request := getTestHttpRequest()

	// Exhaust limiter's tokens
	client.limiter.AllowN(time.Now(), requestsPerSecond)

	// Assert the request fails on the next try
	_, err := client.Do(request)
	require.Error(t, err)

	// Make sure the returned error is the rate limit
	if _, ok := err.(*RateLimitExceeded); !ok {
		t.Fatalf("http client was not rate limited and instead got err=%s", err)
	}

	// Wait for the timer to reset
	time.Sleep(time.Second)

	// Assert no error after waiting for rate limit to pass
	_, err = client.Do(request)
	require.NoError(t, err)
}

func getHttpClientAndLimiter(rps int) *HttpClient {
	limiter := rate.NewLimiter(rate.Limit(rps), rps)
	client := NewHttpClient(limiter)
	return client
}

func getTestHttpRequest() *http.Request {
	return &http.Request{
		URL: &url.URL{
			Scheme: "https",
			Host:   "google.com",
		},
	}
}
