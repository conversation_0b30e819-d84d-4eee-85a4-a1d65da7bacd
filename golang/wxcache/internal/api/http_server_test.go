package api_test

import (
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
	"llif.org/wxcache/internal/api"
	"llif.org/wxcache/pkg/testutil/testsetup"
)

func TestHTTPServerIsAvailable(t *testing.T) {
	c, err := testsetup.NewConfig()
	require.NoError(t, err)

	c.Server.HTTPPort = ":8018"

	svc, err := api.NewAPIService(c)
	require.NoError(t, err)

	server, err := api.NewHttpServer(c, svc)
	require.NoError(t, err)

	go func() {
		if err := server.Start(); err != nil {
			c.Logger.Error("server could not start", "err", err)
		}
	}()

	time.Sleep(time.Second)

	var healthCheckUrl = fmt.Sprintf("http://localhost%s/healthz", c.Server.HTTPPort)
	res, err := http.Get(healthCheckUrl)
	require.NoError(t, err)

	defer res.Body.Close()
	require.Equal(t, http.StatusOK, res.StatusCode)

	require.NoError(t, server.Stop())
}
