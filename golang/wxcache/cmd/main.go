package main

import (
	"context"
	"log/slog"
	"os"
	"os/signal"
	"syscall"

	"llif.org/wxcache/internal/api"
	"llif.org/wxcache/internal/ckey"
	"llif.org/wxcache/internal/config"
	"llif.org/wxcache/internal/config/setup"
	"llif.org/wxcache/internal/msgproc"
	"llif.org/wxcache/internal/msgproc/handler"
	"llif.org/wxcache/internal/service"
	llifaws "llif.org/wxcache/pkg/aws"

	// We import the package to embed the timezone database into the binary.
	// https://cs.opensource.google/go/go/+/refs/tags/go1.22.1:src/time/tzdata/tzdata.go;l=5-18
	_ "time/tzdata"
)

func main() {
	c, err := setup.NewConfig()
	if err != nil {
		panic(err)
	}
	c.Info("setup config", "env", c.RunEnv, "config", c)

	svc, err := api.NewAPIService(c)
	if err != nil {
		panic(err)
	}

	server, err := api.NewHttpServer(c, svc)
	if err != nil {
		panic(err)
	}

	ctx := context.Background()
	ctx = context.WithValue(ctx, ckey.Runtime, ckey.RuntimeMsgProc)

	setupMessaging(ctx, c, svc.EnvironmentService)

	shutdown := make(chan os.Signal, 1)
	go func() {
		if err := server.Start(); err != nil {
			// Shutdown the process if the http server returns an error
			c.Error("server shutdown", "err", err)
			shutdown <- syscall.SIGTERM
		}
	}()

	signal.Notify(shutdown, syscall.SIGINT, syscall.SIGTERM)
	<-shutdown

	c.Info("shutting down server")
}

// A helper function which registers all of the necessary message handlers
// and bounds them to their respective queues.
func setupMessaging(ctx context.Context, c *config.Config, svc service.EnvironmentService) {
	defer func() {
		r := recover() // nil if no panic
		if r != nil {
			slog.Error("recovered from asynchronous messaging setup panic", "err", r)

			// Re-raise panic so the system doesn't go into an undefined state
			panic(r)
		}
	}()
	var (
		prefix                   = c.Messaging.MessagingPrefix
		locationLoadingTopicName = prefix + "location_loading_finished"
		airQualityQueue          = prefix + llifaws.AirQualityQueue
		weatherQueue             = prefix + llifaws.WeatherQueue
		pollenQueue              = prefix + llifaws.PollenQueue
	)

	sqsClient := llifaws.NewSQS(ctx, llifaws.SQSOpts{
		AWSRegion:   c.AWS.Region,
		AWSEndpoint: c.AWS.Endpoint,
		RunEnv:      c.RunEnv,
	})

	aqHandlers := map[string][]msgproc.MessageHandler{
		locationLoadingTopicName: {
			handler.NewAirQualityMessageHandler(c.Logger, svc),
		},
	}

	weatherHandlers := map[string][]msgproc.MessageHandler{
		locationLoadingTopicName: {
			handler.NewWeatherMessageHandler(c.Logger, svc),
		},
	}

	pollenHandlers := map[string][]msgproc.MessageHandler{
		locationLoadingTopicName: {
			handler.NewPollenMessageHandler(c.Logger, svc),
		},
	}

	go msgproc.Consume(ctx, msgproc.MsgprocOpts{
		SQSSvc:   sqsClient,
		Queue:    airQualityQueue,
		Handlers: aqHandlers,
	})
	go msgproc.Consume(ctx, msgproc.MsgprocOpts{
		SQSSvc:   sqsClient,
		Queue:    weatherQueue,
		Handlers: weatherHandlers,
	})
	go msgproc.Consume(ctx, msgproc.MsgprocOpts{
		SQSSvc:   sqsClient,
		Queue:    pollenQueue,
		Handlers: pollenHandlers,
	})
}
