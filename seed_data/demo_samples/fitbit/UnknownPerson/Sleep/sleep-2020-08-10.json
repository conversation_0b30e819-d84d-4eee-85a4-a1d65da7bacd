[{"logId": 28806025727, "dateOfSleep": "2020-09-09", "startTime": "2020-09-09T02:04:00.000", "endTime": "2020-09-09T07:23:30.000", "duration": 19140000, "minutesToFallAsleep": 0, "minutesAsleep": 282, "minutesAwake": 37, "minutesAfterWakeup": 0, "timeInBed": 319, "efficiency": 95, "type": "stages", "infoCode": 0, "levels": {"summary": {"deep": {"count": 2, "minutes": 36, "thirtyDayAvgMinutes": 35}, "wake": {"count": 17, "minutes": 37, "thirtyDayAvgMinutes": 46}, "light": {"count": 19, "minutes": 190, "thirtyDayAvgMinutes": 222}, "rem": {"count": 3, "minutes": 56, "thirtyDayAvgMinutes": 54}}, "data": [{"dateTime": "2020-09-09T02:04:00.000", "level": "wake", "seconds": 240}, {"dateTime": "2020-09-09T02:08:00.000", "level": "light", "seconds": 990}, {"dateTime": "2020-09-09T02:24:30.000", "level": "deep", "seconds": 300}, {"dateTime": "2020-09-09T02:29:30.000", "level": "light", "seconds": 480}, {"dateTime": "2020-09-09T02:37:30.000", "level": "rem", "seconds": 330}, {"dateTime": "2020-09-09T02:43:00.000", "level": "light", "seconds": 1800}, {"dateTime": "2020-09-09T03:13:00.000", "level": "wake", "seconds": 480}, {"dateTime": "2020-09-09T03:21:00.000", "level": "light", "seconds": 1740}, {"dateTime": "2020-09-09T03:50:00.000", "level": "rem", "seconds": 2790}, {"dateTime": "2020-09-09T04:36:30.000", "level": "light", "seconds": 540}, {"dateTime": "2020-09-09T04:45:30.000", "level": "wake", "seconds": 240}, {"dateTime": "2020-09-09T04:49:30.000", "level": "light", "seconds": 690}, {"dateTime": "2020-09-09T05:01:00.000", "level": "deep", "seconds": 1860}, {"dateTime": "2020-09-09T05:32:00.000", "level": "light", "seconds": 3120}, {"dateTime": "2020-09-09T06:24:00.000", "level": "rem", "seconds": 270}, {"dateTime": "2020-09-09T06:28:30.000", "level": "light", "seconds": 210}, {"dateTime": "2020-09-09T06:32:00.000", "level": "wake", "seconds": 540}, {"dateTime": "2020-09-09T06:41:00.000", "level": "light", "seconds": 2550}], "shortData": [{"dateTime": "2020-09-09T02:29:30.000", "level": "wake", "seconds": 120}, {"dateTime": "2020-09-09T04:38:30.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-09-09T04:41:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-09-09T04:52:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-09-09T05:31:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-09-09T05:37:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-09-09T05:48:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-09-09T05:59:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-09-09T06:19:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-09-09T06:56:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-09-09T07:06:00.000", "level": "wake", "seconds": 90}, {"dateTime": "2020-09-09T07:13:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-09-09T07:20:30.000", "level": "wake", "seconds": 180}]}, "mainSleep": true}, {"logId": 28787232267, "dateOfSleep": "2020-09-08", "startTime": "2020-09-08T01:22:00.000", "endTime": "2020-09-08T06:52:30.000", "duration": 19800000, "minutesToFallAsleep": 0, "minutesAsleep": 287, "minutesAwake": 43, "minutesAfterWakeup": 0, "timeInBed": 330, "efficiency": 98, "type": "stages", "infoCode": 0, "levels": {"summary": {"deep": {"count": 1, "minutes": 18, "thirtyDayAvgMinutes": 36}, "wake": {"count": 20, "minutes": 43, "thirtyDayAvgMinutes": 46}, "light": {"count": 19, "minutes": 217, "thirtyDayAvgMinutes": 223}, "rem": {"count": 3, "minutes": 52, "thirtyDayAvgMinutes": 54}}, "data": [{"dateTime": "2020-09-08T01:22:00.000", "level": "wake", "seconds": 360}, {"dateTime": "2020-09-08T01:28:00.000", "level": "light", "seconds": 3510}, {"dateTime": "2020-09-08T02:26:30.000", "level": "deep", "seconds": 1170}, {"dateTime": "2020-09-08T02:46:00.000", "level": "light", "seconds": 5370}, {"dateTime": "2020-09-08T04:15:30.000", "level": "rem", "seconds": 1950}, {"dateTime": "2020-09-08T04:48:00.000", "level": "light", "seconds": 2310}, {"dateTime": "2020-09-08T05:26:30.000", "level": "wake", "seconds": 210}, {"dateTime": "2020-09-08T05:30:00.000", "level": "light", "seconds": 1980}, {"dateTime": "2020-09-08T06:03:00.000", "level": "rem", "seconds": 1200}, {"dateTime": "2020-09-08T06:23:00.000", "level": "wake", "seconds": 390}, {"dateTime": "2020-09-08T06:29:30.000", "level": "light", "seconds": 690}, {"dateTime": "2020-09-08T06:41:00.000", "level": "wake", "seconds": 690}], "shortData": [{"dateTime": "2020-09-08T01:35:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-09-08T01:46:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-09-08T02:07:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-09-08T02:15:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-09-08T02:45:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-09-08T02:51:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-09-08T03:06:30.000", "level": "wake", "seconds": 120}, {"dateTime": "2020-09-08T03:18:30.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-09-08T03:23:00.000", "level": "wake", "seconds": 180}, {"dateTime": "2020-09-08T03:37:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-09-08T03:40:30.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-09-08T03:56:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-09-08T04:38:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-09-08T04:56:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-09-08T05:20:30.000", "level": "wake", "seconds": 90}, {"dateTime": "2020-09-08T05:42:30.000", "level": "wake", "seconds": 60}]}, "mainSleep": true}, {"logId": 28774789480, "dateOfSleep": "2020-09-07", "startTime": "2020-09-07T02:05:30.000", "endTime": "2020-09-07T07:16:30.000", "duration": 18660000, "minutesToFallAsleep": 0, "minutesAsleep": 270, "minutesAwake": 41, "minutesAfterWakeup": 0, "timeInBed": 311, "efficiency": 95, "type": "stages", "infoCode": 0, "levels": {"summary": {"deep": {"count": 1, "minutes": 28, "thirtyDayAvgMinutes": 36}, "wake": {"count": 14, "minutes": 41, "thirtyDayAvgMinutes": 46}, "light": {"count": 14, "minutes": 184, "thirtyDayAvgMinutes": 224}, "rem": {"count": 2, "minutes": 58, "thirtyDayAvgMinutes": 54}}, "data": [{"dateTime": "2020-09-07T02:05:30.000", "level": "wake", "seconds": 300}, {"dateTime": "2020-09-07T02:10:30.000", "level": "light", "seconds": 3780}, {"dateTime": "2020-09-07T03:13:30.000", "level": "rem", "seconds": 600}, {"dateTime": "2020-09-07T03:23:30.000", "level": "light", "seconds": 2010}, {"dateTime": "2020-09-07T03:57:00.000", "level": "wake", "seconds": 300}, {"dateTime": "2020-09-07T04:02:00.000", "level": "light", "seconds": 2640}, {"dateTime": "2020-09-07T04:46:00.000", "level": "wake", "seconds": 270}, {"dateTime": "2020-09-07T04:50:30.000", "level": "light", "seconds": 720}, {"dateTime": "2020-09-07T05:02:30.000", "level": "rem", "seconds": 2940}, {"dateTime": "2020-09-07T05:51:30.000", "level": "light", "seconds": 450}, {"dateTime": "2020-09-07T05:59:00.000", "level": "wake", "seconds": 360}, {"dateTime": "2020-09-07T06:05:00.000", "level": "light", "seconds": 690}, {"dateTime": "2020-09-07T06:16:30.000", "level": "deep", "seconds": 1680}, {"dateTime": "2020-09-07T06:44:30.000", "level": "wake", "seconds": 330}, {"dateTime": "2020-09-07T06:50:00.000", "level": "light", "seconds": 1140}, {"dateTime": "2020-09-07T07:09:00.000", "level": "wake", "seconds": 450}], "shortData": [{"dateTime": "2020-09-07T02:38:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-09-07T03:05:00.000", "level": "wake", "seconds": 90}, {"dateTime": "2020-09-07T03:10:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-09-07T03:22:00.000", "level": "wake", "seconds": 90}, {"dateTime": "2020-09-07T03:29:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-09-07T04:16:30.000", "level": "wake", "seconds": 120}, {"dateTime": "2020-09-07T06:13:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-09-07T07:05:30.000", "level": "wake", "seconds": 60}]}, "mainSleep": true}, {"logId": 28761710790, "dateOfSleep": "2020-09-06", "startTime": "2020-09-06T00:42:30.000", "endTime": "2020-09-06T07:02:30.000", "duration": 22800000, "minutesToFallAsleep": 0, "minutesAsleep": 324, "minutesAwake": 56, "minutesAfterWakeup": 0, "timeInBed": 380, "efficiency": 98, "type": "stages", "infoCode": 0, "levels": {"summary": {"deep": {"count": 3, "minutes": 26, "thirtyDayAvgMinutes": 37}, "wake": {"count": 18, "minutes": 56, "thirtyDayAvgMinutes": 46}, "light": {"count": 19, "minutes": 244, "thirtyDayAvgMinutes": 223}, "rem": {"count": 3, "minutes": 54, "thirtyDayAvgMinutes": 54}}, "data": [{"dateTime": "2020-09-06T00:42:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-09-06T00:43:00.000", "level": "light", "seconds": 4860}, {"dateTime": "2020-09-06T02:04:00.000", "level": "deep", "seconds": 450}, {"dateTime": "2020-09-06T02:11:30.000", "level": "wake", "seconds": 210}, {"dateTime": "2020-09-06T02:15:00.000", "level": "light", "seconds": 2040}, {"dateTime": "2020-09-06T02:49:00.000", "level": "deep", "seconds": 300}, {"dateTime": "2020-09-06T02:54:00.000", "level": "light", "seconds": 480}, {"dateTime": "2020-09-06T03:02:00.000", "level": "wake", "seconds": 570}, {"dateTime": "2020-09-06T03:11:30.000", "level": "light", "seconds": 1920}, {"dateTime": "2020-09-06T03:43:30.000", "level": "deep", "seconds": 840}, {"dateTime": "2020-09-06T03:57:30.000", "level": "light", "seconds": 420}, {"dateTime": "2020-09-06T04:04:30.000", "level": "rem", "seconds": 1800}, {"dateTime": "2020-09-06T04:34:30.000", "level": "light", "seconds": 3420}, {"dateTime": "2020-09-06T05:31:30.000", "level": "rem", "seconds": 810}, {"dateTime": "2020-09-06T05:45:00.000", "level": "light", "seconds": 300}, {"dateTime": "2020-09-06T05:50:00.000", "level": "rem", "seconds": 780}, {"dateTime": "2020-09-06T06:03:00.000", "level": "wake", "seconds": 630}, {"dateTime": "2020-09-06T06:13:30.000", "level": "light", "seconds": 2010}, {"dateTime": "2020-09-06T06:47:00.000", "level": "wake", "seconds": 930}], "shortData": [{"dateTime": "2020-09-06T01:10:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-09-06T01:38:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-09-06T01:44:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-09-06T01:53:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-09-06T02:58:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-09-06T03:17:30.000", "level": "wake", "seconds": 150}, {"dateTime": "2020-09-06T03:22:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-09-06T04:33:30.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-09-06T04:36:30.000", "level": "wake", "seconds": 120}, {"dateTime": "2020-09-06T05:07:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-09-06T05:43:00.000", "level": "wake", "seconds": 120}, {"dateTime": "2020-09-06T06:19:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-09-06T06:33:00.000", "level": "wake", "seconds": 30}]}, "mainSleep": true}, {"logId": 28751749903, "dateOfSleep": "2020-09-05", "startTime": "2020-09-05T02:25:30.000", "endTime": "2020-09-05T09:15:00.000", "duration": 24540000, "minutesToFallAsleep": 0, "minutesAsleep": 373, "minutesAwake": 36, "minutesAfterWakeup": 0, "timeInBed": 409, "efficiency": 94, "type": "stages", "infoCode": 0, "levels": {"summary": {"deep": {"count": 3, "minutes": 55, "thirtyDayAvgMinutes": 36}, "wake": {"count": 23, "minutes": 36, "thirtyDayAvgMinutes": 46}, "light": {"count": 23, "minutes": 228, "thirtyDayAvgMinutes": 223}, "rem": {"count": 6, "minutes": 90, "thirtyDayAvgMinutes": 53}}, "data": [{"dateTime": "2020-09-05T02:25:30.000", "level": "wake", "seconds": 270}, {"dateTime": "2020-09-05T02:30:00.000", "level": "light", "seconds": 1050}, {"dateTime": "2020-09-05T02:47:30.000", "level": "deep", "seconds": 1560}, {"dateTime": "2020-09-05T03:13:30.000", "level": "light", "seconds": 30}, {"dateTime": "2020-09-05T03:14:00.000", "level": "rem", "seconds": 2250}, {"dateTime": "2020-09-05T03:51:30.000", "level": "wake", "seconds": 690}, {"dateTime": "2020-09-05T04:03:00.000", "level": "light", "seconds": 480}, {"dateTime": "2020-09-05T04:11:00.000", "level": "deep", "seconds": 960}, {"dateTime": "2020-09-05T04:27:00.000", "level": "light", "seconds": 1080}, {"dateTime": "2020-09-05T04:45:00.000", "level": "rem", "seconds": 3030}, {"dateTime": "2020-09-05T05:35:30.000", "level": "light", "seconds": 2610}, {"dateTime": "2020-09-05T06:19:00.000", "level": "rem", "seconds": 300}, {"dateTime": "2020-09-05T06:24:00.000", "level": "light", "seconds": 210}, {"dateTime": "2020-09-05T06:27:30.000", "level": "wake", "seconds": 390}, {"dateTime": "2020-09-05T06:34:00.000", "level": "light", "seconds": 6690}, {"dateTime": "2020-09-05T08:25:30.000", "level": "deep", "seconds": 810}, {"dateTime": "2020-09-05T08:39:00.000", "level": "light", "seconds": 2160}], "shortData": [{"dateTime": "2020-09-05T02:37:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-09-05T02:40:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-09-05T03:13:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-09-05T03:29:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-09-05T04:26:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-09-05T04:28:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-09-05T05:14:30.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-09-05T05:34:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-09-05T05:54:30.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-09-05T06:45:30.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-09-05T07:07:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-09-05T07:09:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-09-05T07:28:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-09-05T07:40:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-09-05T07:59:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-09-05T08:02:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-09-05T08:08:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-09-05T08:41:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-09-05T08:47:30.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-09-05T09:10:00.000", "level": "wake", "seconds": 30}]}, "mainSleep": true}, {"logId": 28736593242, "dateOfSleep": "2020-09-04", "startTime": "2020-09-04T00:46:00.000", "endTime": "2020-09-04T07:47:30.000", "duration": 25260000, "minutesToFallAsleep": 0, "minutesAsleep": 327, "minutesAwake": 94, "minutesAfterWakeup": 0, "timeInBed": 421, "efficiency": 86, "type": "stages", "infoCode": 0, "levels": {"summary": {"deep": {"count": 1, "minutes": 35, "thirtyDayAvgMinutes": 36}, "wake": {"count": 13, "minutes": 94, "thirtyDayAvgMinutes": 44}, "light": {"count": 13, "minutes": 241, "thirtyDayAvgMinutes": 222}, "rem": {"count": 3, "minutes": 51, "thirtyDayAvgMinutes": 53}}, "data": [{"dateTime": "2020-09-04T00:46:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-09-04T00:46:30.000", "level": "light", "seconds": 2940}, {"dateTime": "2020-09-04T01:35:30.000", "level": "rem", "seconds": 450}, {"dateTime": "2020-09-04T01:43:00.000", "level": "wake", "seconds": 330}, {"dateTime": "2020-09-04T01:48:30.000", "level": "light", "seconds": 540}, {"dateTime": "2020-09-04T01:57:30.000", "level": "wake", "seconds": 450}, {"dateTime": "2020-09-04T02:05:00.000", "level": "light", "seconds": 360}, {"dateTime": "2020-09-04T02:11:00.000", "level": "wake", "seconds": 600}, {"dateTime": "2020-09-04T02:21:00.000", "level": "light", "seconds": 2310}, {"dateTime": "2020-09-04T02:59:30.000", "level": "rem", "seconds": 960}, {"dateTime": "2020-09-04T03:15:30.000", "level": "wake", "seconds": 1170}, {"dateTime": "2020-09-04T03:35:00.000", "level": "light", "seconds": 210}, {"dateTime": "2020-09-04T03:38:30.000", "level": "wake", "seconds": 2100}, {"dateTime": "2020-09-04T04:13:30.000", "level": "light", "seconds": 3390}, {"dateTime": "2020-09-04T05:10:00.000", "level": "rem", "seconds": 1650}, {"dateTime": "2020-09-04T05:37:30.000", "level": "light", "seconds": 1350}, {"dateTime": "2020-09-04T06:00:00.000", "level": "deep", "seconds": 2130}, {"dateTime": "2020-09-04T06:35:30.000", "level": "light", "seconds": 1560}, {"dateTime": "2020-09-04T07:01:30.000", "level": "wake", "seconds": 210}, {"dateTime": "2020-09-04T07:05:00.000", "level": "light", "seconds": 1800}, {"dateTime": "2020-09-04T07:35:00.000", "level": "wake", "seconds": 390}, {"dateTime": "2020-09-04T07:41:30.000", "level": "light", "seconds": 360}], "shortData": [{"dateTime": "2020-09-04T00:46:00.000", "level": "wake", "seconds": 180}, {"dateTime": "2020-09-04T05:45:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-09-04T06:35:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-09-04T06:37:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-09-04T07:44:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-09-04T07:46:30.000", "level": "wake", "seconds": 60}]}, "mainSleep": true}, {"logId": 28723908377, "dateOfSleep": "2020-09-03", "startTime": "2020-09-03T00:25:00.000", "endTime": "2020-09-03T07:14:00.000", "duration": 24540000, "minutesToFallAsleep": 0, "minutesAsleep": 346, "minutesAwake": 63, "minutesAfterWakeup": 0, "timeInBed": 409, "efficiency": 96, "type": "stages", "infoCode": 0, "levels": {"summary": {"deep": {"count": 4, "minutes": 23, "thirtyDayAvgMinutes": 37}, "wake": {"count": 22, "minutes": 63, "thirtyDayAvgMinutes": 43}, "light": {"count": 23, "minutes": 256, "thirtyDayAvgMinutes": 221}, "rem": {"count": 3, "minutes": 67, "thirtyDayAvgMinutes": 52}}, "data": [{"dateTime": "2020-09-03T00:25:00.000", "level": "wake", "seconds": 330}, {"dateTime": "2020-09-03T00:30:30.000", "level": "light", "seconds": 660}, {"dateTime": "2020-09-03T00:41:30.000", "level": "deep", "seconds": 390}, {"dateTime": "2020-09-03T00:48:00.000", "level": "light", "seconds": 930}, {"dateTime": "2020-09-03T01:03:30.000", "level": "deep", "seconds": 330}, {"dateTime": "2020-09-03T01:09:00.000", "level": "light", "seconds": 1620}, {"dateTime": "2020-09-03T01:36:00.000", "level": "rem", "seconds": 1710}, {"dateTime": "2020-09-03T02:04:30.000", "level": "deep", "seconds": 360}, {"dateTime": "2020-09-03T02:10:30.000", "level": "light", "seconds": 1500}, {"dateTime": "2020-09-03T02:35:30.000", "level": "deep", "seconds": 300}, {"dateTime": "2020-09-03T02:40:30.000", "level": "light", "seconds": 1230}, {"dateTime": "2020-09-03T03:01:00.000", "level": "wake", "seconds": 450}, {"dateTime": "2020-09-03T03:08:30.000", "level": "light", "seconds": 150}, {"dateTime": "2020-09-03T03:11:00.000", "level": "wake", "seconds": 360}, {"dateTime": "2020-09-03T03:17:00.000", "level": "light", "seconds": 570}, {"dateTime": "2020-09-03T03:26:30.000", "level": "wake", "seconds": 270}, {"dateTime": "2020-09-03T03:31:00.000", "level": "light", "seconds": 390}, {"dateTime": "2020-09-03T03:37:30.000", "level": "wake", "seconds": 390}, {"dateTime": "2020-09-03T03:44:00.000", "level": "light", "seconds": 750}, {"dateTime": "2020-09-03T03:56:30.000", "level": "rem", "seconds": 2310}, {"dateTime": "2020-09-03T04:35:00.000", "level": "wake", "seconds": 390}, {"dateTime": "2020-09-03T04:41:30.000", "level": "light", "seconds": 2730}, {"dateTime": "2020-09-03T05:27:00.000", "level": "wake", "seconds": 210}, {"dateTime": "2020-09-03T05:30:30.000", "level": "light", "seconds": 5010}, {"dateTime": "2020-09-03T06:54:00.000", "level": "wake", "seconds": 450}, {"dateTime": "2020-09-03T07:01:30.000", "level": "light", "seconds": 420}, {"dateTime": "2020-09-03T07:08:30.000", "level": "wake", "seconds": 330}], "shortData": [{"dateTime": "2020-09-03T01:15:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-09-03T01:34:30.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-09-03T01:37:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-09-03T02:10:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-09-03T02:20:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-09-03T02:58:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-09-03T04:47:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-09-03T05:38:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-09-03T05:42:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-09-03T05:56:00.000", "level": "wake", "seconds": 90}, {"dateTime": "2020-09-03T06:00:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-09-03T06:36:00.000", "level": "wake", "seconds": 90}, {"dateTime": "2020-09-03T07:04:30.000", "level": "wake", "seconds": 60}]}, "mainSleep": true}, {"logId": 28706580902, "dateOfSleep": "2020-09-02", "startTime": "2020-09-02T02:44:00.000", "endTime": "2020-09-02T07:21:30.000", "duration": 16620000, "minutesToFallAsleep": 0, "minutesAsleep": 254, "minutesAwake": 23, "minutesAfterWakeup": 0, "timeInBed": 277, "efficiency": 96, "type": "stages", "infoCode": 0, "levels": {"summary": {"deep": {"count": 2, "minutes": 27, "thirtyDayAvgMinutes": 37}, "wake": {"count": 14, "minutes": 23, "thirtyDayAvgMinutes": 44}, "light": {"count": 15, "minutes": 171, "thirtyDayAvgMinutes": 223}, "rem": {"count": 3, "minutes": 56, "thirtyDayAvgMinutes": 52}}, "data": [{"dateTime": "2020-09-02T02:44:00.000", "level": "light", "seconds": 180}, {"dateTime": "2020-09-02T02:47:00.000", "level": "deep", "seconds": 1110}, {"dateTime": "2020-09-02T03:05:30.000", "level": "light", "seconds": 240}, {"dateTime": "2020-09-02T03:09:30.000", "level": "wake", "seconds": 450}, {"dateTime": "2020-09-02T03:17:00.000", "level": "light", "seconds": 1980}, {"dateTime": "2020-09-02T03:50:00.000", "level": "deep", "seconds": 570}, {"dateTime": "2020-09-02T03:59:30.000", "level": "light", "seconds": 1560}, {"dateTime": "2020-09-02T04:25:30.000", "level": "rem", "seconds": 2700}, {"dateTime": "2020-09-02T05:10:30.000", "level": "light", "seconds": 6720}, {"dateTime": "2020-09-02T07:02:30.000", "level": "rem", "seconds": 690}, {"dateTime": "2020-09-02T07:14:00.000", "level": "wake", "seconds": 450}], "shortData": [{"dateTime": "2020-09-02T03:23:00.000", "level": "wake", "seconds": 120}, {"dateTime": "2020-09-02T03:59:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-09-02T04:01:30.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-09-02T04:08:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-09-02T05:36:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-09-02T05:55:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-09-02T06:04:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-09-02T06:11:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-09-02T06:22:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-09-02T06:26:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-09-02T06:38:30.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-09-02T07:12:00.000", "level": "wake", "seconds": 30}]}, "mainSleep": true}, {"logId": 28690450343, "dateOfSleep": "2020-09-01", "startTime": "2020-09-01T01:22:00.000", "endTime": "2020-09-01T06:41:30.000", "duration": 19140000, "minutesToFallAsleep": 0, "minutesAsleep": 294, "minutesAwake": 25, "minutesAfterWakeup": 0, "timeInBed": 319, "efficiency": 98, "type": "stages", "infoCode": 0, "levels": {"summary": {"deep": {"count": 2, "minutes": 53, "thirtyDayAvgMinutes": 36}, "wake": {"count": 19, "minutes": 25, "thirtyDayAvgMinutes": 45}, "light": {"count": 18, "minutes": 186, "thirtyDayAvgMinutes": 225}, "rem": {"count": 4, "minutes": 55, "thirtyDayAvgMinutes": 52}}, "data": [{"dateTime": "2020-09-01T01:22:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-09-01T01:22:30.000", "level": "light", "seconds": 1920}, {"dateTime": "2020-09-01T01:54:30.000", "level": "deep", "seconds": 2430}, {"dateTime": "2020-09-01T02:35:00.000", "level": "wake", "seconds": 480}, {"dateTime": "2020-09-01T02:43:00.000", "level": "light", "seconds": 2610}, {"dateTime": "2020-09-01T03:26:30.000", "level": "rem", "seconds": 1170}, {"dateTime": "2020-09-01T03:46:00.000", "level": "light", "seconds": 3660}, {"dateTime": "2020-09-01T04:47:00.000", "level": "rem", "seconds": 1260}, {"dateTime": "2020-09-01T05:08:00.000", "level": "light", "seconds": 1260}, {"dateTime": "2020-09-01T05:29:00.000", "level": "deep", "seconds": 810}, {"dateTime": "2020-09-01T05:42:30.000", "level": "light", "seconds": 2430}, {"dateTime": "2020-09-01T06:23:00.000", "level": "rem", "seconds": 1110}], "shortData": [{"dateTime": "2020-09-01T01:22:00.000", "level": "wake", "seconds": 180}, {"dateTime": "2020-09-01T01:44:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-09-01T02:45:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-09-01T02:47:30.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-09-01T03:40:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-09-01T03:45:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-09-01T03:49:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-09-01T03:52:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-09-01T04:14:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-09-01T04:23:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-09-01T05:08:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-09-01T05:42:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-09-01T05:53:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-09-01T05:58:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-09-01T06:05:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-09-01T06:08:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-09-01T06:16:30.000", "level": "wake", "seconds": 90}, {"dateTime": "2020-09-01T06:39:00.000", "level": "wake", "seconds": 150}]}, "mainSleep": true}, {"logId": 28675891145, "dateOfSleep": "2020-08-31", "startTime": "2020-08-31T01:07:00.000", "endTime": "2020-08-31T06:55:00.000", "duration": 20880000, "minutesToFallAsleep": 0, "minutesAsleep": 311, "minutesAwake": 37, "minutesAfterWakeup": 0, "timeInBed": 348, "efficiency": 96, "type": "stages", "infoCode": 0, "levels": {"summary": {"deep": {"count": 2, "minutes": 38, "thirtyDayAvgMinutes": 36}, "wake": {"count": 19, "minutes": 37, "thirtyDayAvgMinutes": 46}, "light": {"count": 19, "minutes": 230, "thirtyDayAvgMinutes": 225}, "rem": {"count": 5, "minutes": 43, "thirtyDayAvgMinutes": 52}}, "data": [{"dateTime": "2020-08-31T01:07:00.000", "level": "wake", "seconds": 420}, {"dateTime": "2020-08-31T01:14:00.000", "level": "light", "seconds": 1170}, {"dateTime": "2020-08-31T01:33:30.000", "level": "deep", "seconds": 1920}, {"dateTime": "2020-08-31T02:05:30.000", "level": "light", "seconds": 2010}, {"dateTime": "2020-08-31T02:39:00.000", "level": "rem", "seconds": 330}, {"dateTime": "2020-08-31T02:44:30.000", "level": "light", "seconds": 570}, {"dateTime": "2020-08-31T02:54:00.000", "level": "rem", "seconds": 780}, {"dateTime": "2020-08-31T03:07:00.000", "level": "light", "seconds": 2460}, {"dateTime": "2020-08-31T03:48:00.000", "level": "wake", "seconds": 210}, {"dateTime": "2020-08-31T03:51:30.000", "level": "light", "seconds": 3960}, {"dateTime": "2020-08-31T04:57:30.000", "level": "rem", "seconds": 270}, {"dateTime": "2020-08-31T05:02:00.000", "level": "wake", "seconds": 300}, {"dateTime": "2020-08-31T05:07:00.000", "level": "light", "seconds": 1260}, {"dateTime": "2020-08-31T05:28:00.000", "level": "deep", "seconds": 390}, {"dateTime": "2020-08-31T05:34:30.000", "level": "light", "seconds": 630}, {"dateTime": "2020-08-31T05:45:00.000", "level": "rem", "seconds": 1200}, {"dateTime": "2020-08-31T06:05:00.000", "level": "wake", "seconds": 420}, {"dateTime": "2020-08-31T06:12:00.000", "level": "light", "seconds": 2580}], "shortData": [{"dateTime": "2020-08-31T02:05:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-31T02:13:00.000", "level": "wake", "seconds": 90}, {"dateTime": "2020-08-31T02:44:30.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-31T03:07:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-31T03:23:30.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-31T03:33:30.000", "level": "wake", "seconds": 90}, {"dateTime": "2020-08-31T03:40:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-31T03:43:30.000", "level": "wake", "seconds": 90}, {"dateTime": "2020-08-31T03:57:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-31T04:17:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-31T04:28:30.000", "level": "wake", "seconds": 180}, {"dateTime": "2020-08-31T05:09:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-31T05:40:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-31T05:54:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-31T06:47:30.000", "level": "wake", "seconds": 30}]}, "mainSleep": true}, {"logId": 28651528643, "dateOfSleep": "2020-08-29", "startTime": "2020-08-29T02:23:00.000", "endTime": "2020-08-29T07:58:30.000", "duration": 20100000, "minutesToFallAsleep": 0, "minutesAsleep": 291, "minutesAwake": 44, "minutesAfterWakeup": 1, "timeInBed": 335, "efficiency": 91, "type": "stages", "infoCode": 0, "levels": {"summary": {"deep": {"count": 3, "minutes": 42, "thirtyDayAvgMinutes": 36}, "wake": {"count": 20, "minutes": 44, "thirtyDayAvgMinutes": 46}, "light": {"count": 19, "minutes": 205, "thirtyDayAvgMinutes": 226}, "rem": {"count": 3, "minutes": 44, "thirtyDayAvgMinutes": 53}}, "data": [{"dateTime": "2020-08-29T02:23:00.000", "level": "wake", "seconds": 240}, {"dateTime": "2020-08-29T02:27:00.000", "level": "light", "seconds": 2460}, {"dateTime": "2020-08-29T03:08:00.000", "level": "deep", "seconds": 300}, {"dateTime": "2020-08-29T03:13:00.000", "level": "light", "seconds": 3540}, {"dateTime": "2020-08-29T04:12:00.000", "level": "wake", "seconds": 270}, {"dateTime": "2020-08-29T04:16:30.000", "level": "light", "seconds": 120}, {"dateTime": "2020-08-29T04:18:30.000", "level": "rem", "seconds": 2400}, {"dateTime": "2020-08-29T04:58:30.000", "level": "wake", "seconds": 330}, {"dateTime": "2020-08-29T05:04:00.000", "level": "light", "seconds": 3720}, {"dateTime": "2020-08-29T06:06:00.000", "level": "rem", "seconds": 270}, {"dateTime": "2020-08-29T06:10:30.000", "level": "light", "seconds": 1140}, {"dateTime": "2020-08-29T06:29:30.000", "level": "wake", "seconds": 930}, {"dateTime": "2020-08-29T06:45:00.000", "level": "light", "seconds": 570}, {"dateTime": "2020-08-29T06:54:30.000", "level": "deep", "seconds": 1710}, {"dateTime": "2020-08-29T07:23:00.000", "level": "light", "seconds": 1470}, {"dateTime": "2020-08-29T07:47:30.000", "level": "deep", "seconds": 660}], "shortData": [{"dateTime": "2020-08-29T02:46:30.000", "level": "wake", "seconds": 150}, {"dateTime": "2020-08-29T02:51:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-29T03:36:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-29T03:38:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-29T03:41:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-29T04:03:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-29T04:05:30.000", "level": "wake", "seconds": 120}, {"dateTime": "2020-08-29T04:25:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-29T05:36:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-29T05:47:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-29T05:49:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-29T06:10:30.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-29T06:22:30.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-29T07:22:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-29T07:37:30.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-29T07:57:00.000", "level": "wake", "seconds": 90}]}, "mainSleep": true}, {"logId": 28639381801, "dateOfSleep": "2020-08-28", "startTime": "2020-08-28T00:12:30.000", "endTime": "2020-08-28T07:20:00.000", "duration": 25620000, "minutesToFallAsleep": 0, "minutesAsleep": 376, "minutesAwake": 51, "minutesAfterWakeup": 0, "timeInBed": 427, "efficiency": 97, "type": "stages", "infoCode": 0, "levels": {"summary": {"deep": {"count": 4, "minutes": 82, "thirtyDayAvgMinutes": 33}, "wake": {"count": 29, "minutes": 51, "thirtyDayAvgMinutes": 45}, "light": {"count": 30, "minutes": 241, "thirtyDayAvgMinutes": 225}, "rem": {"count": 3, "minutes": 53, "thirtyDayAvgMinutes": 53}}, "data": [{"dateTime": "2020-08-28T00:12:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-28T00:13:00.000", "level": "light", "seconds": 1080}, {"dateTime": "2020-08-28T00:31:00.000", "level": "deep", "seconds": 1890}, {"dateTime": "2020-08-28T01:02:30.000", "level": "light", "seconds": 210}, {"dateTime": "2020-08-28T01:06:00.000", "level": "rem", "seconds": 780}, {"dateTime": "2020-08-28T01:19:00.000", "level": "light", "seconds": 2070}, {"dateTime": "2020-08-28T01:53:30.000", "level": "deep", "seconds": 1950}, {"dateTime": "2020-08-28T02:26:00.000", "level": "light", "seconds": 810}, {"dateTime": "2020-08-28T02:39:30.000", "level": "deep", "seconds": 930}, {"dateTime": "2020-08-28T02:55:00.000", "level": "light", "seconds": 2820}, {"dateTime": "2020-08-28T03:42:00.000", "level": "rem", "seconds": 1020}, {"dateTime": "2020-08-28T03:59:00.000", "level": "light", "seconds": 4380}, {"dateTime": "2020-08-28T05:12:00.000", "level": "rem", "seconds": 1440}, {"dateTime": "2020-08-28T05:36:00.000", "level": "light", "seconds": 3630}, {"dateTime": "2020-08-28T06:36:30.000", "level": "deep", "seconds": 360}, {"dateTime": "2020-08-28T06:42:30.000", "level": "light", "seconds": 960}, {"dateTime": "2020-08-28T06:58:30.000", "level": "wake", "seconds": 1290}], "shortData": [{"dateTime": "2020-08-28T00:18:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-28T00:21:30.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-28T01:02:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-28T01:19:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-28T01:26:00.000", "level": "wake", "seconds": 120}, {"dateTime": "2020-08-28T02:25:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-28T02:53:00.000", "level": "wake", "seconds": 120}, {"dateTime": "2020-08-28T03:00:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-28T03:03:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-28T03:20:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-28T03:25:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-28T03:38:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-28T03:59:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-28T04:04:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-28T04:08:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-28T04:45:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-28T04:47:30.000", "level": "wake", "seconds": 150}, {"dateTime": "2020-08-28T04:51:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-28T04:56:30.000", "level": "wake", "seconds": 120}, {"dateTime": "2020-08-28T05:35:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-28T05:42:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-28T05:51:30.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-28T06:06:00.000", "level": "wake", "seconds": 90}, {"dateTime": "2020-08-28T06:10:00.000", "level": "wake", "seconds": 90}, {"dateTime": "2020-08-28T06:41:30.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-28T06:46:00.000", "level": "wake", "seconds": 90}, {"dateTime": "2020-08-28T06:50:00.000", "level": "wake", "seconds": 90}]}, "mainSleep": true}, {"logId": 28622393865, "dateOfSleep": "2020-08-27", "startTime": "2020-08-27T00:51:30.000", "endTime": "2020-08-27T06:28:30.000", "duration": 20220000, "minutesToFallAsleep": 0, "minutesAsleep": 294, "minutesAwake": 43, "minutesAfterWakeup": 0, "timeInBed": 337, "efficiency": 94, "type": "stages", "infoCode": 0, "levels": {"summary": {"deep": {"count": 2, "minutes": 42, "thirtyDayAvgMinutes": 33}, "wake": {"count": 17, "minutes": 43, "thirtyDayAvgMinutes": 45}, "light": {"count": 19, "minutes": 203, "thirtyDayAvgMinutes": 226}, "rem": {"count": 4, "minutes": 49, "thirtyDayAvgMinutes": 53}}, "data": [{"dateTime": "2020-08-27T00:51:30.000", "level": "wake", "seconds": 630}, {"dateTime": "2020-08-27T01:02:00.000", "level": "light", "seconds": 3000}, {"dateTime": "2020-08-27T01:52:00.000", "level": "deep", "seconds": 630}, {"dateTime": "2020-08-27T02:02:30.000", "level": "light", "seconds": 840}, {"dateTime": "2020-08-27T02:16:30.000", "level": "wake", "seconds": 300}, {"dateTime": "2020-08-27T02:21:30.000", "level": "light", "seconds": 2550}, {"dateTime": "2020-08-27T03:04:00.000", "level": "rem", "seconds": 2220}, {"dateTime": "2020-08-27T03:41:00.000", "level": "light", "seconds": 570}, {"dateTime": "2020-08-27T03:50:30.000", "level": "rem", "seconds": 270}, {"dateTime": "2020-08-27T03:55:00.000", "level": "light", "seconds": 630}, {"dateTime": "2020-08-27T04:05:30.000", "level": "wake", "seconds": 330}, {"dateTime": "2020-08-27T04:11:00.000", "level": "light", "seconds": 630}, {"dateTime": "2020-08-27T04:21:30.000", "level": "deep", "seconds": 1950}, {"dateTime": "2020-08-27T04:54:00.000", "level": "light", "seconds": 3210}, {"dateTime": "2020-08-27T05:47:30.000", "level": "rem", "seconds": 450}, {"dateTime": "2020-08-27T05:55:00.000", "level": "light", "seconds": 30}, {"dateTime": "2020-08-27T05:55:30.000", "level": "wake", "seconds": 390}, {"dateTime": "2020-08-27T06:02:00.000", "level": "light", "seconds": 1230}, {"dateTime": "2020-08-27T06:22:30.000", "level": "wake", "seconds": 360}], "shortData": [{"dateTime": "2020-08-27T01:43:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-27T02:37:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-27T03:04:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-27T03:48:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-27T04:53:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-27T05:08:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-27T05:17:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-27T05:25:30.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-27T05:36:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-27T05:39:00.000", "level": "wake", "seconds": 120}, {"dateTime": "2020-08-27T06:12:00.000", "level": "wake", "seconds": 120}, {"dateTime": "2020-08-27T06:17:00.000", "level": "wake", "seconds": 60}]}, "mainSleep": true}, {"logId": 28609459957, "dateOfSleep": "2020-08-26", "startTime": "2020-08-26T01:05:30.000", "endTime": "2020-08-26T08:19:00.000", "duration": 25980000, "minutesToFallAsleep": 0, "minutesAsleep": 383, "minutesAwake": 50, "minutesAfterWakeup": 0, "timeInBed": 433, "efficiency": 94, "type": "stages", "infoCode": 0, "levels": {"summary": {"deep": {"count": 4, "minutes": 35, "thirtyDayAvgMinutes": 33}, "wake": {"count": 22, "minutes": 50, "thirtyDayAvgMinutes": 45}, "light": {"count": 23, "minutes": 271, "thirtyDayAvgMinutes": 223}, "rem": {"count": 7, "minutes": 77, "thirtyDayAvgMinutes": 51}}, "data": [{"dateTime": "2020-08-26T01:05:30.000", "level": "wake", "seconds": 630}, {"dateTime": "2020-08-26T01:16:00.000", "level": "light", "seconds": 690}, {"dateTime": "2020-08-26T01:27:30.000", "level": "deep", "seconds": 540}, {"dateTime": "2020-08-26T01:36:30.000", "level": "light", "seconds": 1410}, {"dateTime": "2020-08-26T02:00:00.000", "level": "deep", "seconds": 360}, {"dateTime": "2020-08-26T02:06:00.000", "level": "light", "seconds": 270}, {"dateTime": "2020-08-26T02:10:30.000", "level": "wake", "seconds": 450}, {"dateTime": "2020-08-26T02:18:00.000", "level": "light", "seconds": 660}, {"dateTime": "2020-08-26T02:29:00.000", "level": "deep", "seconds": 300}, {"dateTime": "2020-08-26T02:34:00.000", "level": "light", "seconds": 810}, {"dateTime": "2020-08-26T02:47:30.000", "level": "rem", "seconds": 2070}, {"dateTime": "2020-08-26T03:22:00.000", "level": "wake", "seconds": 840}, {"dateTime": "2020-08-26T03:36:00.000", "level": "light", "seconds": 3690}, {"dateTime": "2020-08-26T04:37:30.000", "level": "rem", "seconds": 390}, {"dateTime": "2020-08-26T04:44:00.000", "level": "light", "seconds": 2190}, {"dateTime": "2020-08-26T05:20:30.000", "level": "rem", "seconds": 330}, {"dateTime": "2020-08-26T05:26:00.000", "level": "light", "seconds": 1140}, {"dateTime": "2020-08-26T05:45:00.000", "level": "rem", "seconds": 1920}, {"dateTime": "2020-08-26T06:17:00.000", "level": "wake", "seconds": 420}, {"dateTime": "2020-08-26T06:24:00.000", "level": "light", "seconds": 3780}, {"dateTime": "2020-08-26T07:27:00.000", "level": "deep", "seconds": 930}, {"dateTime": "2020-08-26T07:42:30.000", "level": "light", "seconds": 2190}], "shortData": [{"dateTime": "2020-08-26T01:40:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-26T01:42:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-26T01:51:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-26T03:12:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-26T03:15:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-26T04:02:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-26T04:11:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-26T04:29:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-26T04:52:30.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-26T05:14:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-26T05:29:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-26T06:14:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-26T06:50:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-26T07:09:30.000", "level": "wake", "seconds": 90}, {"dateTime": "2020-08-26T07:13:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-26T07:42:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-26T07:51:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-26T08:18:30.000", "level": "wake", "seconds": 30}]}, "mainSleep": true}, {"logId": 28594096430, "dateOfSleep": "2020-08-25", "startTime": "2020-08-24T23:57:00.000", "endTime": "2020-08-25T05:18:30.000", "duration": 19260000, "minutesToFallAsleep": 0, "minutesAsleep": 271, "minutesAwake": 50, "minutesAfterWakeup": 1, "timeInBed": 321, "efficiency": 94, "type": "stages", "infoCode": 0, "levels": {"summary": {"deep": {"count": 1, "minutes": 32, "thirtyDayAvgMinutes": 33}, "wake": {"count": 17, "minutes": 50, "thirtyDayAvgMinutes": 45}, "light": {"count": 17, "minutes": 202, "thirtyDayAvgMinutes": 225}, "rem": {"count": 2, "minutes": 37, "thirtyDayAvgMinutes": 52}}, "data": [{"dateTime": "2020-08-24T23:57:00.000", "level": "wake", "seconds": 240}, {"dateTime": "2020-08-25T00:01:00.000", "level": "light", "seconds": 1170}, {"dateTime": "2020-08-25T00:20:30.000", "level": "deep", "seconds": 2010}, {"dateTime": "2020-08-25T00:54:00.000", "level": "light", "seconds": 360}, {"dateTime": "2020-08-25T01:00:00.000", "level": "wake", "seconds": 270}, {"dateTime": "2020-08-25T01:04:30.000", "level": "light", "seconds": 1470}, {"dateTime": "2020-08-25T01:29:00.000", "level": "rem", "seconds": 1680}, {"dateTime": "2020-08-25T01:57:00.000", "level": "light", "seconds": 1980}, {"dateTime": "2020-08-25T02:30:00.000", "level": "wake", "seconds": 450}, {"dateTime": "2020-08-25T02:37:30.000", "level": "light", "seconds": 2250}, {"dateTime": "2020-08-25T03:15:00.000", "level": "rem", "seconds": 720}, {"dateTime": "2020-08-25T03:27:00.000", "level": "light", "seconds": 720}, {"dateTime": "2020-08-25T03:39:00.000", "level": "wake", "seconds": 300}, {"dateTime": "2020-08-25T03:44:00.000", "level": "light", "seconds": 480}, {"dateTime": "2020-08-25T03:52:00.000", "level": "wake", "seconds": 420}, {"dateTime": "2020-08-25T03:59:00.000", "level": "light", "seconds": 2280}, {"dateTime": "2020-08-25T04:37:00.000", "level": "wake", "seconds": 450}, {"dateTime": "2020-08-25T04:44:30.000", "level": "light", "seconds": 1740}, {"dateTime": "2020-08-25T05:13:30.000", "level": "wake", "seconds": 300}], "shortData": [{"dateTime": "2020-08-25T00:53:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-25T01:54:00.000", "level": "wake", "seconds": 180}, {"dateTime": "2020-08-25T02:00:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-25T02:05:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-25T02:10:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-25T02:42:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-25T02:45:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-25T02:48:30.000", "level": "wake", "seconds": 120}, {"dateTime": "2020-08-25T02:54:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-25T04:49:00.000", "level": "wake", "seconds": 30}]}, "mainSleep": true}, {"logId": 28583153252, "dateOfSleep": "2020-08-24", "startTime": "2020-08-24T01:51:00.000", "endTime": "2020-08-24T07:32:30.000", "duration": 20460000, "minutesToFallAsleep": 0, "minutesAsleep": 302, "minutesAwake": 39, "minutesAfterWakeup": 0, "timeInBed": 341, "efficiency": 97, "type": "stages", "infoCode": 0, "levels": {"summary": {"deep": {"count": 1, "minutes": 45, "thirtyDayAvgMinutes": 32}, "wake": {"count": 17, "minutes": 39, "thirtyDayAvgMinutes": 45}, "light": {"count": 17, "minutes": 196, "thirtyDayAvgMinutes": 227}, "rem": {"count": 4, "minutes": 61, "thirtyDayAvgMinutes": 52}}, "data": [{"dateTime": "2020-08-24T01:51:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-24T01:51:30.000", "level": "light", "seconds": 2190}, {"dateTime": "2020-08-24T02:28:00.000", "level": "rem", "seconds": 600}, {"dateTime": "2020-08-24T02:38:00.000", "level": "light", "seconds": 2850}, {"dateTime": "2020-08-24T03:25:30.000", "level": "rem", "seconds": 390}, {"dateTime": "2020-08-24T03:32:00.000", "level": "light", "seconds": 420}, {"dateTime": "2020-08-24T03:39:00.000", "level": "wake", "seconds": 420}, {"dateTime": "2020-08-24T03:46:00.000", "level": "light", "seconds": 630}, {"dateTime": "2020-08-24T03:56:30.000", "level": "rem", "seconds": 540}, {"dateTime": "2020-08-24T04:05:30.000", "level": "light", "seconds": 90}, {"dateTime": "2020-08-24T04:07:00.000", "level": "wake", "seconds": 330}, {"dateTime": "2020-08-24T04:12:30.000", "level": "light", "seconds": 780}, {"dateTime": "2020-08-24T04:25:30.000", "level": "deep", "seconds": 2760}, {"dateTime": "2020-08-24T05:11:30.000", "level": "light", "seconds": 5490}, {"dateTime": "2020-08-24T06:43:00.000", "level": "rem", "seconds": 2160}, {"dateTime": "2020-08-24T07:19:00.000", "level": "wake", "seconds": 810}], "shortData": [{"dateTime": "2020-08-24T01:51:00.000", "level": "wake", "seconds": 180}, {"dateTime": "2020-08-24T02:13:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-24T02:37:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-24T02:40:00.000", "level": "wake", "seconds": 120}, {"dateTime": "2020-08-24T02:47:00.000", "level": "wake", "seconds": 120}, {"dateTime": "2020-08-24T03:14:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-24T03:25:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-24T05:11:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-24T05:17:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-24T05:44:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-24T05:57:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-24T06:20:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-24T06:28:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-24T06:34:30.000", "level": "wake", "seconds": 30}]}, "mainSleep": true}, {"logId": 28572024904, "dateOfSleep": "2020-08-23", "startTime": "2020-08-23T01:50:30.000", "endTime": "2020-08-23T08:46:30.000", "duration": 24960000, "minutesToFallAsleep": 0, "minutesAsleep": 364, "minutesAwake": 52, "minutesAfterWakeup": 0, "timeInBed": 416, "efficiency": 96, "type": "stages", "infoCode": 0, "levels": {"summary": {"deep": {"count": 3, "minutes": 65, "thirtyDayAvgMinutes": 29}, "wake": {"count": 19, "minutes": 52, "thirtyDayAvgMinutes": 45}, "light": {"count": 21, "minutes": 251, "thirtyDayAvgMinutes": 225}, "rem": {"count": 3, "minutes": 48, "thirtyDayAvgMinutes": 52}}, "data": [{"dateTime": "2020-08-23T01:50:30.000", "level": "wake", "seconds": 930}, {"dateTime": "2020-08-23T02:06:00.000", "level": "light", "seconds": 1890}, {"dateTime": "2020-08-23T02:37:30.000", "level": "wake", "seconds": 480}, {"dateTime": "2020-08-23T02:45:30.000", "level": "light", "seconds": 2130}, {"dateTime": "2020-08-23T03:21:00.000", "level": "rem", "seconds": 450}, {"dateTime": "2020-08-23T03:28:30.000", "level": "light", "seconds": 90}, {"dateTime": "2020-08-23T03:30:00.000", "level": "wake", "seconds": 420}, {"dateTime": "2020-08-23T03:37:00.000", "level": "light", "seconds": 600}, {"dateTime": "2020-08-23T03:47:00.000", "level": "deep", "seconds": 2640}, {"dateTime": "2020-08-23T04:31:00.000", "level": "light", "seconds": 540}, {"dateTime": "2020-08-23T04:40:00.000", "level": "rem", "seconds": 990}, {"dateTime": "2020-08-23T04:56:30.000", "level": "light", "seconds": 3240}, {"dateTime": "2020-08-23T05:50:30.000", "level": "rem", "seconds": 1440}, {"dateTime": "2020-08-23T06:14:30.000", "level": "light", "seconds": 30}, {"dateTime": "2020-08-23T06:15:00.000", "level": "wake", "seconds": 300}, {"dateTime": "2020-08-23T06:20:00.000", "level": "light", "seconds": 4950}, {"dateTime": "2020-08-23T07:42:30.000", "level": "deep", "seconds": 630}, {"dateTime": "2020-08-23T07:53:00.000", "level": "light", "seconds": 2280}, {"dateTime": "2020-08-23T08:31:00.000", "level": "deep", "seconds": 720}, {"dateTime": "2020-08-23T08:43:00.000", "level": "wake", "seconds": 210}], "shortData": [{"dateTime": "2020-08-23T02:30:00.000", "level": "wake", "seconds": 120}, {"dateTime": "2020-08-23T02:34:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-23T03:39:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-23T04:30:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-23T04:34:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-23T04:57:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-23T05:09:30.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-23T05:29:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-23T06:29:30.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-23T06:39:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-23T07:02:00.000", "level": "wake", "seconds": 120}, {"dateTime": "2020-08-23T07:20:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-23T07:52:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-23T07:58:00.000", "level": "wake", "seconds": 60}]}, "mainSleep": true}, {"logId": 28559109962, "dateOfSleep": "2020-08-22", "startTime": "2020-08-22T01:54:30.000", "endTime": "2020-08-22T08:34:30.000", "duration": 24000000, "minutesToFallAsleep": 0, "minutesAsleep": 349, "minutesAwake": 51, "minutesAfterWakeup": 0, "timeInBed": 400, "efficiency": 96, "type": "stages", "infoCode": 0, "levels": {"summary": {"deep": {"count": 2, "minutes": 52, "thirtyDayAvgMinutes": 27}, "wake": {"count": 23, "minutes": 51, "thirtyDayAvgMinutes": 44}, "light": {"count": 23, "minutes": 205, "thirtyDayAvgMinutes": 227}, "rem": {"count": 4, "minutes": 92, "thirtyDayAvgMinutes": 48}}, "data": [{"dateTime": "2020-08-22T01:54:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-22T01:55:00.000", "level": "light", "seconds": 4890}, {"dateTime": "2020-08-22T03:16:30.000", "level": "deep", "seconds": 1080}, {"dateTime": "2020-08-22T03:34:30.000", "level": "light", "seconds": 2010}, {"dateTime": "2020-08-22T04:08:00.000", "level": "rem", "seconds": 2790}, {"dateTime": "2020-08-22T04:54:30.000", "level": "light", "seconds": 30}, {"dateTime": "2020-08-22T04:55:00.000", "level": "wake", "seconds": 360}, {"dateTime": "2020-08-22T05:01:00.000", "level": "light", "seconds": 150}, {"dateTime": "2020-08-22T05:03:30.000", "level": "deep", "seconds": 2160}, {"dateTime": "2020-08-22T05:39:30.000", "level": "light", "seconds": 1170}, {"dateTime": "2020-08-22T05:59:00.000", "level": "wake", "seconds": 210}, {"dateTime": "2020-08-22T06:02:30.000", "level": "light", "seconds": 450}, {"dateTime": "2020-08-22T06:10:00.000", "level": "rem", "seconds": 2040}, {"dateTime": "2020-08-22T06:44:00.000", "level": "wake", "seconds": 450}, {"dateTime": "2020-08-22T06:51:30.000", "level": "light", "seconds": 4020}, {"dateTime": "2020-08-22T07:58:30.000", "level": "rem", "seconds": 690}, {"dateTime": "2020-08-22T08:10:00.000", "level": "light", "seconds": 420}, {"dateTime": "2020-08-22T08:17:00.000", "level": "wake", "seconds": 1050}], "shortData": [{"dateTime": "2020-08-22T01:54:30.000", "level": "wake", "seconds": 150}, {"dateTime": "2020-08-22T02:10:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-22T02:34:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-22T02:40:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-22T02:45:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-22T02:50:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-22T02:53:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-22T03:34:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-22T03:38:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-22T03:46:30.000", "level": "wake", "seconds": 120}, {"dateTime": "2020-08-22T04:25:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-22T05:38:30.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-22T07:01:30.000", "level": "wake", "seconds": 90}, {"dateTime": "2020-08-22T07:07:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-22T07:12:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-22T07:36:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-22T07:47:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-22T07:52:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-22T08:12:00.000", "level": "wake", "seconds": 30}]}, "mainSleep": true}, {"logId": 28547549729, "dateOfSleep": "2020-08-21", "startTime": "2020-08-21T04:04:00.000", "endTime": "2020-08-21T07:37:30.000", "duration": 12780000, "minutesToFallAsleep": 0, "minutesAsleep": 195, "minutesAwake": 18, "minutesAfterWakeup": 0, "timeInBed": 213, "efficiency": 95, "type": "stages", "infoCode": 0, "levels": {"summary": {"deep": {"count": 2, "minutes": 11, "thirtyDayAvgMinutes": 28}, "wake": {"count": 13, "minutes": 18, "thirtyDayAvgMinutes": 47}, "light": {"count": 13, "minutes": 174, "thirtyDayAvgMinutes": 232}, "rem": {"count": 2, "minutes": 10, "thirtyDayAvgMinutes": 52}}, "data": [{"dateTime": "2020-08-21T04:04:00.000", "level": "wake", "seconds": 540}, {"dateTime": "2020-08-21T04:13:00.000", "level": "light", "seconds": 3000}, {"dateTime": "2020-08-21T05:03:00.000", "level": "deep", "seconds": 300}, {"dateTime": "2020-08-21T05:08:00.000", "level": "light", "seconds": 3480}, {"dateTime": "2020-08-21T06:06:00.000", "level": "deep", "seconds": 390}, {"dateTime": "2020-08-21T06:12:30.000", "level": "light", "seconds": 3540}, {"dateTime": "2020-08-21T07:11:30.000", "level": "rem", "seconds": 330}, {"dateTime": "2020-08-21T07:17:00.000", "level": "light", "seconds": 150}, {"dateTime": "2020-08-21T07:19:30.000", "level": "rem", "seconds": 330}, {"dateTime": "2020-08-21T07:25:00.000", "level": "light", "seconds": 750}], "shortData": [{"dateTime": "2020-08-21T05:09:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-21T05:19:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-21T05:34:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-21T05:37:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-21T05:52:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-21T06:12:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-21T06:18:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-21T06:27:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-21T06:49:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-21T07:16:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-21T07:25:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-21T07:36:00.000", "level": "wake", "seconds": 90}]}, "mainSleep": true}, {"logId": 28533241456, "dateOfSleep": "2020-08-20", "startTime": "2020-08-20T00:27:30.000", "endTime": "2020-08-20T07:01:30.000", "duration": 23640000, "minutesToFallAsleep": 0, "minutesAsleep": 348, "minutesAwake": 46, "minutesAfterWakeup": 0, "timeInBed": 394, "efficiency": 94, "type": "stages", "infoCode": 0, "levels": {"summary": {"deep": {"count": 3, "minutes": 17, "thirtyDayAvgMinutes": 30}, "wake": {"count": 25, "minutes": 46, "thirtyDayAvgMinutes": 47}, "light": {"count": 27, "minutes": 254, "thirtyDayAvgMinutes": 230}, "rem": {"count": 3, "minutes": 77, "thirtyDayAvgMinutes": 49}}, "data": [{"dateTime": "2020-08-20T00:27:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-20T00:28:00.000", "level": "light", "seconds": 2220}, {"dateTime": "2020-08-20T01:05:00.000", "level": "deep", "seconds": 330}, {"dateTime": "2020-08-20T01:10:30.000", "level": "rem", "seconds": 1980}, {"dateTime": "2020-08-20T01:43:30.000", "level": "light", "seconds": 450}, {"dateTime": "2020-08-20T01:51:00.000", "level": "wake", "seconds": 210}, {"dateTime": "2020-08-20T01:54:30.000", "level": "light", "seconds": 1530}, {"dateTime": "2020-08-20T02:20:00.000", "level": "deep", "seconds": 480}, {"dateTime": "2020-08-20T02:28:00.000", "level": "light", "seconds": 150}, {"dateTime": "2020-08-20T02:30:30.000", "level": "wake", "seconds": 270}, {"dateTime": "2020-08-20T02:35:00.000", "level": "light", "seconds": 1860}, {"dateTime": "2020-08-20T03:06:00.000", "level": "rem", "seconds": 1080}, {"dateTime": "2020-08-20T03:24:00.000", "level": "light", "seconds": 690}, {"dateTime": "2020-08-20T03:35:30.000", "level": "wake", "seconds": 390}, {"dateTime": "2020-08-20T03:42:00.000", "level": "light", "seconds": 3210}, {"dateTime": "2020-08-20T04:35:30.000", "level": "wake", "seconds": 240}, {"dateTime": "2020-08-20T04:39:30.000", "level": "light", "seconds": 360}, {"dateTime": "2020-08-20T04:45:30.000", "level": "rem", "seconds": 1650}, {"dateTime": "2020-08-20T05:13:00.000", "level": "light", "seconds": 2070}, {"dateTime": "2020-08-20T05:47:30.000", "level": "deep", "seconds": 270}, {"dateTime": "2020-08-20T05:52:00.000", "level": "light", "seconds": 3960}, {"dateTime": "2020-08-20T06:58:00.000", "level": "wake", "seconds": 210}], "shortData": [{"dateTime": "2020-08-20T00:27:30.000", "level": "wake", "seconds": 90}, {"dateTime": "2020-08-20T00:35:00.000", "level": "wake", "seconds": 120}, {"dateTime": "2020-08-20T00:39:00.000", "level": "wake", "seconds": 120}, {"dateTime": "2020-08-20T02:27:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-20T02:43:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-20T02:51:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-20T03:59:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-20T04:12:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-20T04:15:00.000", "level": "wake", "seconds": 120}, {"dateTime": "2020-08-20T04:33:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-20T05:11:00.000", "level": "wake", "seconds": 120}, {"dateTime": "2020-08-20T05:15:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-20T05:28:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-20T05:57:30.000", "level": "wake", "seconds": 90}, {"dateTime": "2020-08-20T06:01:30.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-20T06:09:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-20T06:19:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-20T06:35:30.000", "level": "wake", "seconds": 150}, {"dateTime": "2020-08-20T06:40:00.000", "level": "wake", "seconds": 150}, {"dateTime": "2020-08-20T06:44:00.000", "level": "wake", "seconds": 60}]}, "mainSleep": true}, {"logId": 28519492369, "dateOfSleep": "2020-08-19", "startTime": "2020-08-19T00:55:30.000", "endTime": "2020-08-19T06:52:30.000", "duration": 21420000, "minutesToFallAsleep": 0, "minutesAsleep": 316, "minutesAwake": 41, "minutesAfterWakeup": 0, "timeInBed": 357, "efficiency": 92, "type": "stages", "infoCode": 0, "levels": {"summary": {"deep": {"count": 4, "minutes": 25, "thirtyDayAvgMinutes": 30}, "wake": {"count": 24, "minutes": 41, "thirtyDayAvgMinutes": 48}, "light": {"count": 25, "minutes": 246, "thirtyDayAvgMinutes": 228}, "rem": {"count": 4, "minutes": 45, "thirtyDayAvgMinutes": 50}}, "data": [{"dateTime": "2020-08-19T00:55:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-19T00:56:00.000", "level": "light", "seconds": 1440}, {"dateTime": "2020-08-19T01:20:00.000", "level": "deep", "seconds": 420}, {"dateTime": "2020-08-19T01:27:00.000", "level": "light", "seconds": 900}, {"dateTime": "2020-08-19T01:42:00.000", "level": "deep", "seconds": 480}, {"dateTime": "2020-08-19T01:50:00.000", "level": "rem", "seconds": 930}, {"dateTime": "2020-08-19T02:05:30.000", "level": "light", "seconds": 2970}, {"dateTime": "2020-08-19T02:55:00.000", "level": "deep", "seconds": 360}, {"dateTime": "2020-08-19T03:01:00.000", "level": "light", "seconds": 330}, {"dateTime": "2020-08-19T03:06:30.000", "level": "wake", "seconds": 210}, {"dateTime": "2020-08-19T03:10:00.000", "level": "light", "seconds": 870}, {"dateTime": "2020-08-19T03:24:30.000", "level": "wake", "seconds": 450}, {"dateTime": "2020-08-19T03:32:00.000", "level": "light", "seconds": 4680}, {"dateTime": "2020-08-19T04:50:00.000", "level": "deep", "seconds": 360}, {"dateTime": "2020-08-19T04:56:00.000", "level": "light", "seconds": 1110}, {"dateTime": "2020-08-19T05:14:30.000", "level": "rem", "seconds": 330}, {"dateTime": "2020-08-19T05:20:00.000", "level": "light", "seconds": 3870}, {"dateTime": "2020-08-19T06:24:30.000", "level": "rem", "seconds": 1470}, {"dateTime": "2020-08-19T06:49:00.000", "level": "wake", "seconds": 210}], "shortData": [{"dateTime": "2020-08-19T01:03:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-19T01:08:30.000", "level": "wake", "seconds": 180}, {"dateTime": "2020-08-19T01:25:30.000", "level": "wake", "seconds": 90}, {"dateTime": "2020-08-19T01:30:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-19T02:16:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-19T02:20:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-19T02:22:30.000", "level": "wake", "seconds": 180}, {"dateTime": "2020-08-19T02:29:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-19T03:00:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-19T03:13:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-19T03:40:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-19T03:50:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-19T03:56:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-19T04:19:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-19T04:36:00.000", "level": "wake", "seconds": 150}, {"dateTime": "2020-08-19T04:57:30.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-19T05:12:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-19T05:30:30.000", "level": "wake", "seconds": 180}, {"dateTime": "2020-08-19T05:56:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-19T06:29:00.000", "level": "wake", "seconds": 30}]}, "mainSleep": true}, {"logId": 28487203856, "dateOfSleep": "2020-08-17", "startTime": "2020-08-17T00:54:00.000", "endTime": "2020-08-17T06:38:30.000", "duration": 20640000, "minutesToFallAsleep": 0, "minutesAsleep": 305, "minutesAwake": 39, "minutesAfterWakeup": 0, "timeInBed": 344, "efficiency": 94, "type": "stages", "infoCode": 0, "levels": {"summary": {"deep": {"count": 3, "minutes": 15, "thirtyDayAvgMinutes": 32}, "wake": {"count": 16, "minutes": 39, "thirtyDayAvgMinutes": 49}, "light": {"count": 19, "minutes": 246, "thirtyDayAvgMinutes": 225}, "rem": {"count": 5, "minutes": 44, "thirtyDayAvgMinutes": 51}}, "data": [{"dateTime": "2020-08-17T00:54:00.000", "level": "light", "seconds": 1980}, {"dateTime": "2020-08-17T01:27:00.000", "level": "wake", "seconds": 360}, {"dateTime": "2020-08-17T01:33:00.000", "level": "light", "seconds": 2130}, {"dateTime": "2020-08-17T02:08:30.000", "level": "deep", "seconds": 360}, {"dateTime": "2020-08-17T02:14:30.000", "level": "light", "seconds": 150}, {"dateTime": "2020-08-17T02:17:00.000", "level": "rem", "seconds": 420}, {"dateTime": "2020-08-17T02:24:00.000", "level": "light", "seconds": 1410}, {"dateTime": "2020-08-17T02:47:30.000", "level": "wake", "seconds": 480}, {"dateTime": "2020-08-17T02:55:30.000", "level": "light", "seconds": 1530}, {"dateTime": "2020-08-17T03:21:00.000", "level": "rem", "seconds": 1080}, {"dateTime": "2020-08-17T03:39:00.000", "level": "light", "seconds": 1950}, {"dateTime": "2020-08-17T04:11:30.000", "level": "rem", "seconds": 300}, {"dateTime": "2020-08-17T04:16:30.000", "level": "light", "seconds": 690}, {"dateTime": "2020-08-17T04:28:00.000", "level": "rem", "seconds": 990}, {"dateTime": "2020-08-17T04:44:30.000", "level": "wake", "seconds": 330}, {"dateTime": "2020-08-17T04:50:00.000", "level": "light", "seconds": 180}, {"dateTime": "2020-08-17T04:53:00.000", "level": "wake", "seconds": 450}, {"dateTime": "2020-08-17T05:00:30.000", "level": "light", "seconds": 3630}, {"dateTime": "2020-08-17T06:01:00.000", "level": "wake", "seconds": 210}, {"dateTime": "2020-08-17T06:04:30.000", "level": "light", "seconds": 900}, {"dateTime": "2020-08-17T06:19:30.000", "level": "deep", "seconds": 420}, {"dateTime": "2020-08-17T06:26:30.000", "level": "light", "seconds": 600}, {"dateTime": "2020-08-17T06:36:30.000", "level": "deep", "seconds": 120}], "shortData": [{"dateTime": "2020-08-17T01:09:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-17T01:48:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-17T02:22:30.000", "level": "wake", "seconds": 90}, {"dateTime": "2020-08-17T02:26:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-17T02:30:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-17T03:16:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-17T03:28:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-17T03:44:30.000", "level": "wake", "seconds": 90}, {"dateTime": "2020-08-17T05:24:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-17T05:33:30.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-17T06:26:30.000", "level": "wake", "seconds": 30}]}, "mainSleep": true}, {"logId": 28475743700, "dateOfSleep": "2020-08-16", "startTime": "2020-08-16T01:20:00.000", "endTime": "2020-08-16T08:50:30.000", "duration": 27000000, "minutesToFallAsleep": 0, "minutesAsleep": 392, "minutesAwake": 58, "minutesAfterWakeup": 0, "timeInBed": 450, "efficiency": 98, "type": "stages", "infoCode": 0, "levels": {"summary": {"deep": {"count": 2, "minutes": 14, "thirtyDayAvgMinutes": 35}, "wake": {"count": 24, "minutes": 58, "thirtyDayAvgMinutes": 47}, "light": {"count": 25, "minutes": 324, "thirtyDayAvgMinutes": 209}, "rem": {"count": 3, "minutes": 54, "thirtyDayAvgMinutes": 50}}, "data": [{"dateTime": "2020-08-16T01:20:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-16T01:20:30.000", "level": "light", "seconds": 4170}, {"dateTime": "2020-08-16T02:30:00.000", "level": "deep", "seconds": 330}, {"dateTime": "2020-08-16T02:35:30.000", "level": "light", "seconds": 600}, {"dateTime": "2020-08-16T02:45:30.000", "level": "wake", "seconds": 480}, {"dateTime": "2020-08-16T02:53:30.000", "level": "light", "seconds": 2730}, {"dateTime": "2020-08-16T03:39:00.000", "level": "wake", "seconds": 450}, {"dateTime": "2020-08-16T03:46:30.000", "level": "light", "seconds": 420}, {"dateTime": "2020-08-16T03:53:30.000", "level": "rem", "seconds": 1230}, {"dateTime": "2020-08-16T04:14:00.000", "level": "light", "seconds": 3210}, {"dateTime": "2020-08-16T05:07:30.000", "level": "deep", "seconds": 570}, {"dateTime": "2020-08-16T05:17:00.000", "level": "light", "seconds": 1650}, {"dateTime": "2020-08-16T05:44:30.000", "level": "wake", "seconds": 210}, {"dateTime": "2020-08-16T05:48:00.000", "level": "rem", "seconds": 1470}, {"dateTime": "2020-08-16T06:12:30.000", "level": "light", "seconds": 660}, {"dateTime": "2020-08-16T06:23:30.000", "level": "wake", "seconds": 330}, {"dateTime": "2020-08-16T06:29:00.000", "level": "light", "seconds": 2490}, {"dateTime": "2020-08-16T07:10:30.000", "level": "wake", "seconds": 270}, {"dateTime": "2020-08-16T07:15:00.000", "level": "light", "seconds": 780}, {"dateTime": "2020-08-16T07:28:00.000", "level": "wake", "seconds": 390}, {"dateTime": "2020-08-16T07:34:30.000", "level": "light", "seconds": 3630}, {"dateTime": "2020-08-16T08:35:00.000", "level": "rem", "seconds": 570}, {"dateTime": "2020-08-16T08:44:30.000", "level": "light", "seconds": 90}, {"dateTime": "2020-08-16T08:46:00.000", "level": "wake", "seconds": 270}], "shortData": [{"dateTime": "2020-08-16T01:20:00.000", "level": "wake", "seconds": 180}, {"dateTime": "2020-08-16T02:35:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-16T03:00:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-16T03:08:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-16T03:15:00.000", "level": "wake", "seconds": 150}, {"dateTime": "2020-08-16T04:15:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-16T04:19:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-16T05:16:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-16T05:20:00.000", "level": "wake", "seconds": 150}, {"dateTime": "2020-08-16T06:20:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-16T06:33:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-16T06:42:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-16T07:04:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-16T07:07:00.000", "level": "wake", "seconds": 90}, {"dateTime": "2020-08-16T07:37:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-16T08:26:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-16T08:31:00.000", "level": "wake", "seconds": 30}]}, "mainSleep": true}, {"logId": 28461739342, "dateOfSleep": "2020-08-15", "startTime": "2020-08-15T00:13:30.000", "endTime": "2020-08-15T05:53:00.000", "duration": 20340000, "minutesToFallAsleep": 0, "minutesAsleep": 302, "minutesAwake": 37, "minutesAfterWakeup": 0, "timeInBed": 339, "efficiency": 95, "type": "stages", "infoCode": 0, "levels": {"summary": {"deep": {"count": 1, "minutes": 23, "thirtyDayAvgMinutes": 38}, "wake": {"count": 17, "minutes": 37, "thirtyDayAvgMinutes": 49}, "light": {"count": 17, "minutes": 245, "thirtyDayAvgMinutes": 202}, "rem": {"count": 2, "minutes": 34, "thirtyDayAvgMinutes": 53}}, "data": [{"dateTime": "2020-08-15T00:13:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-15T00:14:00.000", "level": "light", "seconds": 7560}, {"dateTime": "2020-08-15T02:20:00.000", "level": "rem", "seconds": 990}, {"dateTime": "2020-08-15T02:36:30.000", "level": "wake", "seconds": 210}, {"dateTime": "2020-08-15T02:40:00.000", "level": "light", "seconds": 3630}, {"dateTime": "2020-08-15T03:40:30.000", "level": "wake", "seconds": 360}, {"dateTime": "2020-08-15T03:46:30.000", "level": "light", "seconds": 3330}, {"dateTime": "2020-08-15T04:42:00.000", "level": "deep", "seconds": 1380}, {"dateTime": "2020-08-15T05:05:00.000", "level": "rem", "seconds": 1080}, {"dateTime": "2020-08-15T05:23:00.000", "level": "light", "seconds": 1110}, {"dateTime": "2020-08-15T05:41:30.000", "level": "wake", "seconds": 690}], "shortData": [{"dateTime": "2020-08-15T00:13:30.000", "level": "wake", "seconds": 90}, {"dateTime": "2020-08-15T01:14:30.000", "level": "wake", "seconds": 90}, {"dateTime": "2020-08-15T01:39:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-15T01:52:00.000", "level": "wake", "seconds": 120}, {"dateTime": "2020-08-15T01:56:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-15T02:42:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-15T02:46:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-15T02:57:00.000", "level": "wake", "seconds": 120}, {"dateTime": "2020-08-15T03:13:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-15T03:59:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-15T04:12:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-15T04:18:30.000", "level": "wake", "seconds": 90}, {"dateTime": "2020-08-15T05:24:00.000", "level": "wake", "seconds": 120}, {"dateTime": "2020-08-15T05:37:30.000", "level": "wake", "seconds": 60}]}, "mainSleep": true}, {"logId": 28450120263, "dateOfSleep": "2020-08-14", "startTime": "2020-08-14T00:38:30.000", "endTime": "2020-08-14T05:47:00.000", "duration": 18480000, "minutesToFallAsleep": 0, "minutesAsleep": 266, "minutesAwake": 42, "minutesAfterWakeup": 0, "timeInBed": 308, "efficiency": 93, "type": "stages", "infoCode": 0, "levels": {"summary": {"deep": {"count": 2, "minutes": 27, "thirtyDayAvgMinutes": 41}, "wake": {"count": 17, "minutes": 42, "thirtyDayAvgMinutes": 51}, "light": {"count": 16, "minutes": 193, "thirtyDayAvgMinutes": 204}, "rem": {"count": 5, "minutes": 46, "thirtyDayAvgMinutes": 55}}, "data": [{"dateTime": "2020-08-14T00:38:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-14T00:39:00.000", "level": "light", "seconds": 6450}, {"dateTime": "2020-08-14T02:26:30.000", "level": "rem", "seconds": 2340}, {"dateTime": "2020-08-14T03:05:30.000", "level": "light", "seconds": 240}, {"dateTime": "2020-08-14T03:09:30.000", "level": "deep", "seconds": 450}, {"dateTime": "2020-08-14T03:17:00.000", "level": "light", "seconds": 510}, {"dateTime": "2020-08-14T03:25:30.000", "level": "deep", "seconds": 1170}, {"dateTime": "2020-08-14T03:45:00.000", "level": "light", "seconds": 150}, {"dateTime": "2020-08-14T03:47:30.000", "level": "rem", "seconds": 270}, {"dateTime": "2020-08-14T03:52:00.000", "level": "light", "seconds": 1680}, {"dateTime": "2020-08-14T04:20:00.000", "level": "wake", "seconds": 540}, {"dateTime": "2020-08-14T04:29:00.000", "level": "light", "seconds": 2670}, {"dateTime": "2020-08-14T05:13:30.000", "level": "rem", "seconds": 480}, {"dateTime": "2020-08-14T05:21:30.000", "level": "light", "seconds": 390}, {"dateTime": "2020-08-14T05:28:00.000", "level": "wake", "seconds": 1140}], "shortData": [{"dateTime": "2020-08-14T01:32:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-14T02:10:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-14T02:33:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-14T03:00:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-14T03:04:00.000", "level": "wake", "seconds": 90}, {"dateTime": "2020-08-14T03:07:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-14T03:16:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-14T03:19:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-14T04:10:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-14T04:16:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-14T04:42:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-14T04:47:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-14T05:19:00.000", "level": "wake", "seconds": 150}, {"dateTime": "2020-08-14T05:25:00.000", "level": "wake", "seconds": 30}]}, "mainSleep": true}, {"logId": 28438342723, "dateOfSleep": "2020-08-13", "startTime": "2020-08-13T02:29:00.000", "endTime": "2020-08-13T06:50:30.000", "duration": 15660000, "minutesToFallAsleep": 0, "minutesAsleep": 226, "minutesAwake": 35, "minutesAfterWakeup": 0, "timeInBed": 261, "efficiency": 94, "type": "stages", "infoCode": 0, "levels": {"summary": {"deep": {"count": 1, "minutes": 28, "thirtyDayAvgMinutes": 45}, "wake": {"count": 18, "minutes": 35, "thirtyDayAvgMinutes": 56}, "light": {"count": 19, "minutes": 157, "thirtyDayAvgMinutes": 220}, "rem": {"count": 4, "minutes": 41, "thirtyDayAvgMinutes": 60}}, "data": [{"dateTime": "2020-08-13T02:29:00.000", "level": "light", "seconds": 180}, {"dateTime": "2020-08-13T02:32:00.000", "level": "deep", "seconds": 1800}, {"dateTime": "2020-08-13T03:02:00.000", "level": "light", "seconds": 4110}, {"dateTime": "2020-08-13T04:10:30.000", "level": "rem", "seconds": 1650}, {"dateTime": "2020-08-13T04:38:00.000", "level": "light", "seconds": 4500}, {"dateTime": "2020-08-13T05:53:00.000", "level": "wake", "seconds": 570}, {"dateTime": "2020-08-13T06:02:30.000", "level": "light", "seconds": 570}, {"dateTime": "2020-08-13T06:12:00.000", "level": "rem", "seconds": 660}, {"dateTime": "2020-08-13T06:23:00.000", "level": "light", "seconds": 510}, {"dateTime": "2020-08-13T06:31:30.000", "level": "rem", "seconds": 330}, {"dateTime": "2020-08-13T06:37:00.000", "level": "light", "seconds": 210}, {"dateTime": "2020-08-13T06:40:30.000", "level": "wake", "seconds": 600}], "shortData": [{"dateTime": "2020-08-13T03:00:30.000", "level": "wake", "seconds": 90}, {"dateTime": "2020-08-13T03:04:30.000", "level": "wake", "seconds": 120}, {"dateTime": "2020-08-13T03:11:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-13T03:37:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-13T03:39:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-13T03:47:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-13T03:58:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-13T04:21:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-13T04:46:30.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-13T05:05:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-13T05:26:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-13T05:35:30.000", "level": "wake", "seconds": 90}, {"dateTime": "2020-08-13T05:39:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-13T05:45:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-13T06:20:30.000", "level": "wake", "seconds": 150}, {"dateTime": "2020-08-13T06:26:00.000", "level": "wake", "seconds": 60}]}, "mainSleep": true}, {"logId": 28426574047, "dateOfSleep": "2020-08-12", "startTime": "2020-08-12T01:20:00.000", "endTime": "2020-08-12T07:30:30.000", "duration": 22200000, "minutesToFallAsleep": 0, "minutesAsleep": 313, "minutesAwake": 57, "minutesAfterWakeup": 4, "timeInBed": 370, "efficiency": 96, "type": "stages", "infoCode": 0, "levels": {"summary": {"deep": {"count": 1, "minutes": 35, "thirtyDayAvgMinutes": 50}, "wake": {"count": 20, "minutes": 57, "thirtyDayAvgMinutes": 56}, "light": {"count": 20, "minutes": 210, "thirtyDayAvgMinutes": 225}, "rem": {"count": 2, "minutes": 68, "thirtyDayAvgMinutes": 56}}, "data": [{"dateTime": "2020-08-12T01:20:00.000", "level": "wake", "seconds": 360}, {"dateTime": "2020-08-12T01:26:00.000", "level": "light", "seconds": 390}, {"dateTime": "2020-08-12T01:32:30.000", "level": "deep", "seconds": 2130}, {"dateTime": "2020-08-12T02:08:00.000", "level": "wake", "seconds": 210}, {"dateTime": "2020-08-12T02:11:30.000", "level": "light", "seconds": 2160}, {"dateTime": "2020-08-12T02:47:30.000", "level": "rem", "seconds": 2310}, {"dateTime": "2020-08-12T03:26:00.000", "level": "wake", "seconds": 1200}, {"dateTime": "2020-08-12T03:46:00.000", "level": "light", "seconds": 3630}, {"dateTime": "2020-08-12T04:46:30.000", "level": "rem", "seconds": 1770}, {"dateTime": "2020-08-12T05:16:00.000", "level": "light", "seconds": 7770}, {"dateTime": "2020-08-12T07:25:30.000", "level": "wake", "seconds": 300}], "shortData": [{"dateTime": "2020-08-12T02:16:30.000", "level": "wake", "seconds": 90}, {"dateTime": "2020-08-12T02:19:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-12T04:09:00.000", "level": "wake", "seconds": 150}, {"dateTime": "2020-08-12T04:13:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-12T04:16:00.000", "level": "wake", "seconds": 90}, {"dateTime": "2020-08-12T04:30:30.000", "level": "wake", "seconds": 120}, {"dateTime": "2020-08-12T04:34:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-12T05:19:30.000", "level": "wake", "seconds": 150}, {"dateTime": "2020-08-12T05:48:00.000", "level": "wake", "seconds": 90}, {"dateTime": "2020-08-12T06:13:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-12T06:16:00.000", "level": "wake", "seconds": 120}, {"dateTime": "2020-08-12T06:35:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-12T06:38:30.000", "level": "wake", "seconds": 90}, {"dateTime": "2020-08-12T06:56:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-12T07:09:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-12T07:15:00.000", "level": "wake", "seconds": 120}]}, "mainSleep": true}, {"logId": 28408189132, "dateOfSleep": "2020-08-11", "startTime": "2020-08-11T00:37:00.000", "endTime": "2020-08-11T07:53:00.000", "duration": 26160000, "minutesToFallAsleep": 0, "minutesAsleep": 359, "minutesAwake": 77, "minutesAfterWakeup": 0, "timeInBed": 436, "efficiency": 94, "type": "stages", "infoCode": 0, "levels": {"summary": {"deep": {"count": 2, "minutes": 54, "thirtyDayAvgMinutes": 45}, "wake": {"count": 20, "minutes": 77, "thirtyDayAvgMinutes": 35}, "light": {"count": 21, "minutes": 265, "thirtyDayAvgMinutes": 184}, "rem": {"count": 4, "minutes": 40, "thirtyDayAvgMinutes": 72}}, "data": [{"dateTime": "2020-08-11T00:37:00.000", "level": "light", "seconds": 3000}, {"dateTime": "2020-08-11T01:27:00.000", "level": "deep", "seconds": 1110}, {"dateTime": "2020-08-11T01:45:30.000", "level": "light", "seconds": 1020}, {"dateTime": "2020-08-11T02:02:30.000", "level": "rem", "seconds": 570}, {"dateTime": "2020-08-11T02:12:00.000", "level": "light", "seconds": 1140}, {"dateTime": "2020-08-11T02:31:00.000", "level": "rem", "seconds": 570}, {"dateTime": "2020-08-11T02:40:30.000", "level": "light", "seconds": 2910}, {"dateTime": "2020-08-11T03:29:00.000", "level": "wake", "seconds": 810}, {"dateTime": "2020-08-11T03:42:30.000", "level": "light", "seconds": 120}, {"dateTime": "2020-08-11T03:44:30.000", "level": "wake", "seconds": 990}, {"dateTime": "2020-08-11T04:01:00.000", "level": "light", "seconds": 660}, {"dateTime": "2020-08-11T04:12:00.000", "level": "wake", "seconds": 330}, {"dateTime": "2020-08-11T04:17:30.000", "level": "light", "seconds": 690}, {"dateTime": "2020-08-11T04:29:00.000", "level": "wake", "seconds": 1140}, {"dateTime": "2020-08-11T04:48:00.000", "level": "light", "seconds": 3120}, {"dateTime": "2020-08-11T05:40:00.000", "level": "rem", "seconds": 1440}, {"dateTime": "2020-08-11T06:04:00.000", "level": "wake", "seconds": 240}, {"dateTime": "2020-08-11T06:08:00.000", "level": "light", "seconds": 1170}, {"dateTime": "2020-08-11T06:27:30.000", "level": "deep", "seconds": 2190}, {"dateTime": "2020-08-11T07:04:00.000", "level": "light", "seconds": 2940}], "shortData": [{"dateTime": "2020-08-11T02:15:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-11T02:19:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-11T02:37:30.000", "level": "wake", "seconds": 180}, {"dateTime": "2020-08-11T02:47:00.000", "level": "wake", "seconds": 150}, {"dateTime": "2020-08-11T03:04:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-11T04:24:00.000", "level": "wake", "seconds": 120}, {"dateTime": "2020-08-11T05:47:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-11T07:03:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-11T07:07:00.000", "level": "wake", "seconds": 120}, {"dateTime": "2020-08-11T07:18:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-11T07:20:30.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-11T07:26:00.000", "level": "wake", "seconds": 180}, {"dateTime": "2020-08-11T07:30:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-11T07:39:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-11T07:52:30.000", "level": "wake", "seconds": 30}]}, "mainSleep": true}, {"logId": 28394477665, "dateOfSleep": "2020-08-10", "startTime": "2020-08-10T01:07:00.000", "endTime": "2020-08-10T06:43:30.000", "duration": 20160000, "minutesToFallAsleep": 0, "minutesAsleep": 301, "minutesAwake": 35, "minutesAfterWakeup": 0, "timeInBed": 336, "efficiency": 96, "type": "stages", "infoCode": 0, "levels": {"summary": {"deep": {"count": 2, "minutes": 45, "thirtyDayAvgMinutes": 0}, "wake": {"count": 16, "minutes": 35, "thirtyDayAvgMinutes": 0}, "light": {"count": 17, "minutes": 184, "thirtyDayAvgMinutes": 0}, "rem": {"count": 4, "minutes": 72, "thirtyDayAvgMinutes": 0}}, "data": [{"dateTime": "2020-08-10T01:07:00.000", "level": "wake", "seconds": 720}, {"dateTime": "2020-08-10T01:19:00.000", "level": "light", "seconds": 1560}, {"dateTime": "2020-08-10T01:45:00.000", "level": "wake", "seconds": 300}, {"dateTime": "2020-08-10T01:50:00.000", "level": "light", "seconds": 1710}, {"dateTime": "2020-08-10T02:18:30.000", "level": "rem", "seconds": 2280}, {"dateTime": "2020-08-10T02:56:30.000", "level": "light", "seconds": 4200}, {"dateTime": "2020-08-10T04:06:30.000", "level": "rem", "seconds": 1740}, {"dateTime": "2020-08-10T04:35:30.000", "level": "wake", "seconds": 330}, {"dateTime": "2020-08-10T04:41:00.000", "level": "light", "seconds": 90}, {"dateTime": "2020-08-10T04:42:30.000", "level": "deep", "seconds": 2370}, {"dateTime": "2020-08-10T05:22:00.000", "level": "light", "seconds": 420}, {"dateTime": "2020-08-10T05:29:00.000", "level": "rem", "seconds": 360}, {"dateTime": "2020-08-10T05:35:00.000", "level": "light", "seconds": 1950}, {"dateTime": "2020-08-10T06:07:30.000", "level": "deep", "seconds": 360}, {"dateTime": "2020-08-10T06:13:30.000", "level": "light", "seconds": 1800}], "shortData": [{"dateTime": "2020-08-10T01:59:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-10T02:05:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-10T02:53:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-10T03:28:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-10T03:37:30.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-10T03:45:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-10T05:21:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-10T05:23:30.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-10T05:43:30.000", "level": "wake", "seconds": 90}, {"dateTime": "2020-08-10T05:51:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-10T06:18:30.000", "level": "wake", "seconds": 90}, {"dateTime": "2020-08-10T06:24:30.000", "level": "wake", "seconds": 90}, {"dateTime": "2020-08-10T06:41:00.000", "level": "wake", "seconds": 150}]}, "mainSleep": true}]