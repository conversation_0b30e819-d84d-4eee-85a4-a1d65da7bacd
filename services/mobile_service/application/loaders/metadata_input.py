from dataclasses import dataclass
from typing import List, Optional, Union

from pydantic import AwareDatetime, Field

from services.base.domain.annotated_types import NonEmptyStr
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.constants.value_limits import MetadataTagValueLimit
from services.base.domain.enums.application import Application
from services.base.domain.enums.metadata import DataIntegrity, DataQuality, Organization, Service
from services.base.domain.schemas.metadata import MetadataFields
from services.base.domain.schemas.shared import BaseDataModel


@dataclass(frozen=True)
class MetadataTagsFields:
    TAG = DocumentLabels.TAG


class MetadataTags(BaseDataModel):
    tag: str = Field(
        alias=MetadataTagsFields.TAG, min_length=MetadataTagValueLimit.MINIMUM, max_length=MetadataTagValueLimit.MAXIMUM
    )


class MetadataInputModel(BaseDataModel):
    organization: Organization = Field(alias=MetadataFields.ORGANIZATION)
    data_integrity: DataIntegrity = Field(alias=MetadataFields.DATA_INTEGRITY, default=DataIntegrity.LOW)
    important: bool = Field(alias=MetadataFields.IMPORTANT, default=False)
    urgent: bool = Field(alias=MetadataFields.URGENT, default=False)
    service: Optional[Union[NonEmptyStr, Service]] = Field(alias=MetadataFields.SERVICE, default=None)
    data_quality: Optional[DataQuality] = Field(alias=MetadataFields.DATA_QUALITY, default=None)
    data_proxy: Optional[NonEmptyStr] = Field(alias=MetadataFields.DATA_PROXY, default=None)
    sync_device: Optional[NonEmptyStr] = Field(alias=MetadataFields.SYNC_DEVICE, default=None)
    sync_software: Optional[Application] = Field(alias=MetadataFields.SYNC_SOFTWARE, default=None)
    sensor: Optional[NonEmptyStr] = Field(alias=MetadataFields.SENSOR, default=None)
    tags: Optional[List[MetadataTags]] = Field(alias=MetadataFields.TAGS, default=None, min_length=1)
    favorited_at: Optional[AwareDatetime] = Field(alias=MetadataFields.FAVORITED_AT, default=None)


class MetadataInputBoundary(BaseDataModel):
    metadata: MetadataInputModel = Field(alias=DocumentLabels.METADATA)
