from pydantic import Field

from services.base.domain.schemas.air_quality import AirQualityAQI, AirQualityFields, AirQualityPollutants
from services.base.domain.schemas.environment import EnvironmentMetadata
from services.base.domain.schemas.shared import CoordinatesModel, TimestampModel


class AirQualityBucket(TimestampModel):
    pollutants: AirQualityPollutants = Field(alias=AirQualityFields.POLLUTANTS)
    aqi: AirQualityAQI = Field(alias=AirQualityFields.AQI)
    coordinates: CoordinatesModel = Field(alias=AirQualityFields.COORDINATES)
    metadata: EnvironmentMetadata = Field(alias=AirQualityFields.METADATA)
