from enum import StrEnum

from pydantic_ai import Agent
from pydantic_ai.models import Model

from services.base.application.use_case_base import UseCaseBase
from services.base.domain.annotated_types import NonEmptyStr
from services.base.domain.enums.event_type import EventType
from services.base.domain.schemas.shared import BaseDataModel
from services.data_service.application.use_cases.ai.classify_event_type_use_case import ClassifyEventTypeUseCase
from services.data_service.application.use_cases.ai.prompts.pick_category_prompt import generate_pick_category_prompt


class CategoryModelOutput(BaseDataModel):
    category: str


class ClassifyEventCategoryInputBoundary(BaseDataModel):
    query: NonEmptyStr


class ClassifyEventCategoryOutput(BaseDataModel):
    type: EventType
    category: StrEnum


class ClassifyEventCategoryUseCase(UseCaseBase):
    def __init__(self, model: Model):
        self._model = model
        self._type_classifier = ClassifyEventTypeUseCase(model)

    async def execute(self, input_boundary: ClassifyEventCategoryInputBoundary) -> ClassifyEventCategoryOutput:
        prompt_schema = await self._type_classifier.execute(query=input_boundary.query)

        event_type = EventType(prompt_schema.event_type.type_id())
        categories = [category.value for category in prompt_schema.category_enum]
        category_prompt = generate_pick_category_prompt(event_type=event_type, categories=categories)

        category_agent = Agent(model=self._model, instructions=category_prompt)
        category_result = await category_agent.run(user_prompt=input_boundary.query)

        return ClassifyEventCategoryOutput(
            type=EventType(event_type), category=prompt_schema.category_enum(category_result.output.strip().lower())
        )
