import asyncio
from collections.abc import MutableSequence, Sequence
from uuid import UUID

from services.base.application.database.depr_job_service import DeprJobService
from services.base.application.exceptions import ConflictingActionInProgress
from services.base.application.utils.data_task_orchestration import DataTaskOrchestration
from services.base.domain.enums.document_type import DocumentType
from services.base.domain.enums.metadata import Organization
from services.base.domain.enums.os_task_states import OSTaskStates
from services.base.domain.enums.os_task_types import OSTaskTypes
from services.base.domain.repository.document_repository import DocumentRepository
from services.base.domain.repository.member_user_os_tasks_repository import MemberUserOSTasksRepository
from services.base.domain.repository.member_user_repository import MemberUserRepository
from services.base.domain.schemas.events.document_base import Document
from services.base.domain.schemas.member_user.member_user_os_tasks import MemberUserOSTasks
from services.base.domain.schemas.query.builders.common_query_adjustments import (
    CommonLeafQueries,
    CommonQueryAdjustments,
)
from services.base.domain.schemas.query.query import Query
from services.base.domain.schemas.query.type_query import TypeQuery
from services.data_service.application.services.asset_service import AssetService


class DeleteUserDataUseCase:
    def __init__(
        self,
        tasks_repository: MemberUserOSTasksRepository,
        job_service: DeprJobService,
        asset_service: AssetService,
        user_repository: MemberUserRepository,
        document_repository: DocumentRepository,
    ):
        self._job_svc = job_service
        self._task_repo = tasks_repository
        self._asset_service = asset_service
        self._user_repo = user_repository
        self._document_repo = document_repository

    async def execute_async(self, user_uuid: UUID, query: Query | None = None) -> None:
        if not await self._can_delete(user_uuid=user_uuid):
            raise ConflictingActionInProgress(
                "Can't proceed with deletion - upload or another deletion are already in progress"
            )

        delete_assets: bool = False
        if not query:
            # If no query is provided, delete all data from all v3 data types, including assets
            query = Query(
                type_queries=[
                    TypeQuery(
                        domain_types=self._get_v3_domain_types(),
                        query=CommonLeafQueries.owner_id_value_query(user_uuid=user_uuid),
                    )
                ],
            )
            delete_assets = True
        else:
            query = CommonQueryAdjustments.add_user_uuid_to_query(query=query, user_uuid=user_uuid)

        task_id: str = await self._document_repo.delete_by_query(query=query)
        await self._register_deletion_task(user_uuid=user_uuid, task_id=task_id)

        if delete_assets:
            asyncio.create_task(self._asset_service.delete_user_assets(user_uuid=user_uuid))

        return None

    async def _register_deletion_task(self, user_uuid: UUID, task_id: str) -> None:
        if not await self._user_repo.get_by_uuid(user_uuid=user_uuid):
            return

        await self._task_repo.insert_or_update(
            [
                MemberUserOSTasks(
                    task_id=task_id,
                    user_uuid=user_uuid,
                    task_type=OSTaskTypes.DELETE_BY_QUERY,
                    state=OSTaskStates.SCHEDULED,
                )
            ]
        )

    async def _can_delete(self, user_uuid: UUID) -> bool:
        return all(
            [
                await DataTaskOrchestration.can_delete_uploaded_user_data_in_db(
                    user_uuid=user_uuid,
                    job_service=self._job_svc,
                    os_tasks_repository=self._task_repo,
                    organization=org,
                )
                for org in Organization
            ]
        )

    def _get_v3_domain_types(self) -> Sequence[type[Document]]:
        domain_types: MutableSequence[type[Document]] = []
        for data_type in DocumentType:
            domain_types.append(data_type.to_domain_model())
        return domain_types
