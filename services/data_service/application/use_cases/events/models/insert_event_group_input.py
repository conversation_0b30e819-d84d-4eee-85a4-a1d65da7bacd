from __future__ import annotations

from typing import Literal

from pydantic import Field, computed_field

from services.base.domain.annotated_types import NonEmptyStr
from services.base.domain.content_hash import Hasher
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.activity import ActivityFields
from services.base.domain.schemas.events.event import Event, EventFields, EventValueLimits
from services.base.domain.schemas.events.event_group import EventGroupIdentifier
from services.data_service.application.use_cases.events.models.body_metric.insert_body_metric_inputs import (
    InsertBloodGlucoseInput,
    InsertBloodPressureInput,
    InsertBodyMetricInput,
)
from services.data_service.application.use_cases.events.models.content.insert_content_inputs import (
    InsertAudioInput,
    InsertContentInput,
    InsertImageInput,
    InsertInteractiveInput,
    InsertTextInput,
    InsertVideoInput,
)
from services.data_service.application.use_cases.events.models.exercise.insert_exercise_inputs import (
    InsertCardioInput,
    InsertExerciseInput,
    InsertStrengthInput,
)
from services.data_service.application.use_cases.events.models.feeling.insert_feeling_inputs import (
    InsertEmotionInput,
    InsertStressInput,
)
from services.data_service.application.use_cases.events.models.insert_activity_input import InsertActivityInput
from services.data_service.application.use_cases.events.models.insert_event_input import (
    EventInsertionContext,
    InsertEventInput,
)
from services.data_service.application.use_cases.events.models.insert_medication_input import InsertMedicationInput
from services.data_service.application.use_cases.events.models.insert_note_input import InsertNoteInput
from services.data_service.application.use_cases.events.models.insert_person_input import InsertPersonInput
from services.data_service.application.use_cases.events.models.insert_sleep_v3_input import InsertSleepV3Input
from services.data_service.application.use_cases.events.models.insert_symptom_input import InsertSymptomInput
from services.data_service.application.use_cases.events.models.nutrition.insert_nutrition_inputs import (
    InsertDrinkInput,
    InsertFoodInput,
    InsertSupplementInput,
)


class InsertEventGroupInput(InsertEventInput, EventGroupIdentifier):
    type: Literal[DataType.EventGroup] = Field(alias=EventFields.TYPE)
    events: list[InsertEventInputs] = Field(min_length=1)
    note: NonEmptyStr | None = Field(
        alias=ActivityFields.NOTE, default=None, max_length=EventValueLimits.MAX_NOTE_LENGTH, min_length=1
    )

    def to_domain(self, ctx: EventInsertionContext) -> Event:
        raise NotImplementedError(
            "Groups are not implemented yet. The problem is here with the nested asset references"
        )

    @computed_field()
    @property
    def content_hash(self) -> str:
        return Hasher.fields_sha256(fields=[self.name, self.timestamp.isoformat(), *[e.name for e in self.events]])


BodyMetricInsertInputs = InsertBloodGlucoseInput | InsertBloodPressureInput | InsertBodyMetricInput

ContentInsertInputs = (
    InsertAudioInput
    | InsertContentInput
    | InsertImageInput
    | InsertInteractiveInput
    | InsertTextInput
    | InsertVideoInput
)

ExerciseInsertInputs = InsertCardioInput | InsertExerciseInput | InsertStrengthInput

FeelingsInsertInputs = InsertEmotionInput | InsertStressInput

NutritionInsertInputs = InsertDrinkInput | InsertFoodInput | InsertSupplementInput

InsertEventInputs = (
    BodyMetricInsertInputs
    | ContentInsertInputs
    | ExerciseInsertInputs
    | FeelingsInsertInputs
    | NutritionInsertInputs
    | InsertActivityInput
    | InsertSleepV3Input
    | InsertEventGroupInput
    | InsertNoteInput
    | InsertSymptomInput
    | InsertMedicationInput
    | InsertPersonInput
)
