import logging
from typing import List
from uuid import UUID

from services.base.application.database.depr_event_repository import DeprEventRepository
from services.base.application.database.depr_job_service import DeprJobService
from services.base.application.database.models.filter_types import (
    <PERSON><PERSON>ermsFilter,
    UserUUIDTermsFilter,
)
from services.base.application.database.models.filters import FilterBuilder, Filters
from services.base.application.exceptions import (
    BadRequestException,
    ConflictingActionInProgress,
)
from services.base.application.use_case_base import UseCaseBase
from services.base.application.utils.data_task_orchestration import DataTaskOrchestration
from services.base.domain.enums.metadata import Organization
from services.base.domain.enums.os_task_states import OSTaskStates
from services.base.domain.enums.os_task_types import OSTaskTypes
from services.base.domain.repository.member_user_os_tasks_repository import MemberUserOSTasksRepository
from services.base.domain.schemas.member_user.member_user_os_tasks import MemberUserOSTasks
from services.data_service.application.enums.deletable_data_type import DeletableDataType


class DeleteByFilterUseCase(UseCaseBase):
    def __init__(
        self,
        os_tasks_repository: MemberUserOSTasksRepository,
        event_repo: DeprEventRepository,
        job_service: DeprJobService,
    ):
        self._os_tasks_repository = os_tasks_repository
        self._event_repo = event_repo
        self._job_service = job_service

    async def execute(
        self,
        user_uuid: UUID,
        data_types: List[DeletableDataType],
        organizations: List[Organization],
    ) -> str:
        can_proceed = True
        for org in organizations:
            can_proceed = (
                await DataTaskOrchestration.can_delete_uploaded_user_data_in_db(
                    user_uuid=user_uuid,
                    job_service=self._job_service,
                    organization=org,
                    os_tasks_repository=self._os_tasks_repository,
                )
                and can_proceed
            )
        if not can_proceed:
            raise ConflictingActionInProgress(
                "Can't proceed with deletion - upload or another deletion are already in progress"
            )

        if not data_types:
            data_types = [data_type for data_type in DeletableDataType]

        for data_type in data_types:
            data_schema = data_type.to_domain_model()
            filters = Filters()
            filters.must_filters.with_filters(
                [
                    OrganizationTermsFilter(value=[org.value for org in organizations]),
                    UserUUIDTermsFilter(value=[str(user_uuid)]),
                ]
            )
            try:
                task_id = await self._event_repo.delete_by_query(
                    user_uuid=user_uuid,
                    data_schema=data_schema,
                    filters=Filters(
                        must_filters=FilterBuilder(
                            terms_filters=(
                                [
                                    OrganizationTermsFilter(value=[org.value for org in organizations]),
                                    UserUUIDTermsFilter(value=[str(user_uuid)]),
                                ]
                                if organizations
                                else None
                            )
                        ),
                    ),
                )
                await self._os_tasks_repository.insert_or_update(
                    [
                        MemberUserOSTasks(
                            task_id=task_id,
                            user_uuid=user_uuid,
                            task_type=OSTaskTypes.DELETE_BY_QUERY,
                            state=OSTaskStates.SCHEDULED,
                        )
                    ]
                )
            except Exception as err:
                logging.exception(
                    (
                        f"Failed to schedule deletion for user: {user_uuid},"
                        f" data_type: {data_type}, organizations: {organizations}"
                        f" err: {err}"
                    ),
                )
                raise BadRequestException("Could not schedule deletion.")

        return "Successfully scheduled deletion."
