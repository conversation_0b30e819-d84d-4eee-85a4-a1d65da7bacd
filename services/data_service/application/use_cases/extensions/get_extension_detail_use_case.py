from uuid import UUID
from xml.sax.expatreader import version

from services.base.application.exceptions import NoContentException
from services.base.domain.repository.extension_detail_repository import ExtensionDetailRepository
from services.base.domain.repository.extension_schema_repository import ExtensionSchemaRepository
from services.base.domain.schemas.extensions.extension_detail import ExtensionDetail
from services.base.domain.schemas.shared import BaseDataModel


class GetExtensionDetailUseCaseOutputBoundary(BaseDataModel):
    extension: ExtensionDetail
    input_schema: dict
    output_schema: dict


class GetExtensionDetailUseCase:
    def __init__(
        self,
        extension_detail_repository: ExtensionDetailRepository,
        extension_schema_repository: ExtensionSchemaRepository,
    ):
        self._extension_detail_repository = extension_detail_repository
        self._extension_schema_repository = extension_schema_repository

    async def execute_async(
        self,
        extension_id: UUID,
    ) -> GetExtensionDetailUseCaseOutputBoundary:
        extension = await self._extension_detail_repository.get_by_extension_id(extension_id=extension_id)
        if not extension:
            raise NoContentException(f"Extension with given id {extension_id} was not found.")
        schema = await self._extension_schema_repository.get_by_extension_and_version(
            extension_id=extension_id, version=extension.version
        )
        if not schema:
            raise NoContentException(f"Extension schema version {version} of extension {extension_id} was not found.")
        return GetExtensionDetailUseCaseOutputBoundary(
            extension=extension, input_schema=schema.input_schema, output_schema=schema.output_schema
        )
