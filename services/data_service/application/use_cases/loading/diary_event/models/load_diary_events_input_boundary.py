from typing import List

from pydantic import Field

from services.data_service.application.use_cases.loading.diary_event.models.load_diary_events_input import (
    LoadDiaryEventsInput,
)
from services.data_service.application.use_cases.loading.metadata_input import MetadataInputBoundary


class LoadDiaryEventsInputBoundary(MetadataInputBoundary):
    documents: List[LoadDiaryEventsInput] = Field(min_length=1)
