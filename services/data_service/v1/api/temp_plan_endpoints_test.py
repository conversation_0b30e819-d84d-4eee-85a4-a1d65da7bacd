import json
from typing import Any, AsyncGenerator, Sequence

import pytest
from starlette import status

from services.base.api.authentication.token_handling import generate_access_token
from services.base.api.responses.common_document_responses import CommonDocumentsResponse
from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.application.utils.urls import join_as_url
from services.base.domain.repository.temp_plan_repository import TempPlanRepository
from services.base.domain.schemas.member_user.member_user import MemberUser
from services.base.domain.schemas.temp_plan import (
    TempPlan,
    TempPlanFields,
)
from services.base.tests.domain.builders.temp_plan_builder import (
    TempPlanBuilder,
    TempPlanMetadataBuilder,
    TempPlanStreakBuilder,
)
from services.data_service.api.tests.common_rpc_calls import (
    _call_get_endpoint,
    _call_patch_endpoint,
    _call_post_endpoint,
)
from services.data_service.api.tests.utils import TestUtils
from services.data_service.api.urls import PlanEndpointUrls
from services.data_service.application.use_cases.plans.models.load_temp_plan_input_boundary import LoadTempPlanInput
from services.data_service.application.use_cases.plans.models.update_temp_plan_input_boundary import UpdateTempPlanInput
from services.data_service.conftest import UserFactoryCallable
from services.data_service.v1.api.builders.load_temp_plan_api_request_input_builder import (
    LoadTempPlanAPIRequestInputBuilder,
)
from services.data_service.v1.api.models.output.temp_plan_api_output import TempPlanAPIOutput
from services.data_service.v1.api.models.request.plans.load_temp_plan_api_request_input import (
    LoadTempPlanAPIRequestInput,
)
from services.data_service.v1.api.models.request.plans.update_temp_plan_api_request_input import (
    UpdateTempPlanAPIRequestInput,
)


class TestTempPlanCRUD:
    @pytest.fixture
    async def user_with_temp_plans(
        self, temp_plan_repo: TempPlanRepository, user_factory: UserFactoryCallable
    ) -> AsyncGenerator[tuple[Sequence[TempPlan], MemberUser], Any]:
        user: MemberUser = await user_factory()
        plans = (
            TempPlanBuilder()
            .with_metadata(metadata=TempPlanMetadataBuilder().with_user_uuid(user_uuid=user.user_uuid).build())
            .build_n()
        )
        inserted_plans = await temp_plan_repo.insert(plans=plans, force_strong_consistency=True)

        yield inserted_plans, user

        # Teardown
        await temp_plan_repo.delete_by_id(ids=[p.id for p in plans])

    async def test_update_temp_plan_by_id_should_pass(self, user_with_temp_plans):
        # Arrange
        plans, user = user_with_temp_plans
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}
        input_plans = []

        for plan in plans:
            input_plan = plan.model_copy(
                update={
                    TempPlanFields.NAME_OF_PLAN: PrimitiveTypesGenerator.generate_random_string(),
                    TempPlanFields.MESSAGE: PrimitiveTypesGenerator.generate_random_string(),
                    TempPlanFields.STREAK: TempPlanStreakBuilder().build(),
                    TempPlanFields.NEXT_SCHEDULED_AT: PrimitiveTypesGenerator.generate_random_aware_datetime(),
                    TempPlanFields.LAST_MODIFIED_AT: PrimitiveTypesGenerator.generate_random_aware_datetime(),
                }
            )

            input_plans.append(input_plan)

        request_url = join_as_url(base_url=PlanEndpointUrls.TEMP, query_params={"plan_ids": [p.id for p in plans]})

        # Act
        response = await _call_patch_endpoint(
            request_url=request_url,
            json=json.loads(
                UpdateTempPlanAPIRequestInput(
                    documents=UpdateTempPlanInput.multi_map(models=input_plans)
                ).model_dump_json()
            ),
            headers=headers,
        )

        # Assert
        assert response.status_code == status.HTTP_200_OK
        updated_plans = CommonDocumentsResponse[TempPlanAPIOutput](**response.json()).documents

        input_plan: TempPlan
        updated_plan: TempPlanAPIOutput
        for input_plan, updated_plan in zip(input_plans, updated_plans):
            updated_at = updated_plan.system_properties.updated_at
            assert updated_at
            assert TestUtils.is_date_within_one_minute(updated_at)
            assert updated_plan.name_of_plan == input_plan.name_of_plan
            assert updated_plan.message == input_plan.message
            assert updated_plan.streak == input_plan.streak
            assert updated_plan.next_scheduled_at == input_plan.next_scheduled_at
            assert updated_plan.last_modified_at == input_plan.last_modified_at

    async def test_update_temp_plan_by_id_when_duplication_fields_do_not_change_should_pass(self, user_with_temp_plans):
        # Arrange
        plans, user = user_with_temp_plans
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}

        updated_plan = UpdateTempPlanInput.map(model=plans[0])
        new_streak = PrimitiveTypesGenerator.generate_random_int(max_value=50)
        updated_plan.streak.streak = new_streak

        # Act
        response = await _call_patch_endpoint(
            request_url=PlanEndpointUrls.TEMP,
            json=json.loads(UpdateTempPlanAPIRequestInput(documents=[updated_plan]).model_dump_json()),
            headers=headers,
        )
        # Assert
        assert response.status_code == status.HTTP_200_OK
        updated_plans = CommonDocumentsResponse[TempPlanAPIOutput](**response.json()).documents
        assert len(updated_plans) == 1
        output_plan = updated_plans[0]
        assert output_plan.system_properties.updated_at
        assert TestUtils.is_date_within_one_minute(output_plan.system_properties.updated_at)
        assert output_plan.name_of_plan == updated_plan.name_of_plan
        assert output_plan.message == updated_plan.message
        assert output_plan.streak == updated_plan.streak
        assert output_plan.next_scheduled_at == updated_plan.next_scheduled_at
        assert output_plan.last_modified_at == updated_plan.last_modified_at

    async def test_update_temp_plan_by_id_when_update_makes_duplication_should_fail(
        self, temp_plan_repo: TempPlanRepository, user_factory: UserFactoryCallable
    ):
        # Arrange
        user = await user_factory()
        plans = (
            TempPlanBuilder()
            .with_metadata(metadata=TempPlanMetadataBuilder().with_user_uuid(user_uuid=user.user_uuid).build())
            .with_interval(PrimitiveTypesGenerator.generate_random_int(min_value=0, max_value=50))
            .with_start_time(PrimitiveTypesGenerator.generate_random_aware_datetime())
            .build_n(n=2)
        )
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}
        inserted_plans = await temp_plan_repo.insert(plans=plans, force_strong_consistency=True)
        updated_plan = UpdateTempPlanInput.map(model=inserted_plans[0])
        # Since those two events differ only in name (important for duplication check) we change the name to be the same
        updated_plan.name_of_plan = inserted_plans[1].name_of_plan

        # Act
        response = await _call_patch_endpoint(
            request_url=PlanEndpointUrls.TEMP,
            json=json.loads(UpdateTempPlanAPIRequestInput(documents=[updated_plan]).model_dump_json()),
            headers=headers,
        )

        # Assert
        assert response.status_code == status.HTTP_400_BAD_REQUEST

    async def test_archive_temp_plan_should_pass(self, user_with_temp_plans):
        # Arrange
        plans, user = user_with_temp_plans
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}
        request_url = join_as_url(
            base_url=PlanEndpointUrls.TEMP_ARCHIVE, query_params={"plan_ids": [p.id for p in plans]}
        )

        # Act
        response = await _call_patch_endpoint(request_url=request_url, headers=headers, json=None)

        # Assert
        assert response.status_code == status.HTTP_200_OK
        archived_plans = CommonDocumentsResponse[TempPlanAPIOutput](**response.json()).documents
        for plan in archived_plans:
            archived_at = plan.metadata.archived_at
            assert archived_at
            assert TestUtils.is_date_within_one_minute(date=archived_at)

    async def test_list_temp_plans_endpoint_should_pass(self, user_with_temp_plans: tuple[list[TempPlan], MemberUser]):
        # Arrange
        expected_plans, user = user_with_temp_plans
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}

        # Act
        response = await _call_get_endpoint(request_url=PlanEndpointUrls.TEMP, headers=headers)

        # Assert
        assert response.status_code == status.HTTP_200_OK
        actual_plans = CommonDocumentsResponse[TempPlanAPIOutput](**response.json()).documents
        assert len(actual_plans) == len(expected_plans)
        sorted_expected_plans = sorted(
            expected_plans, key=lambda p: (p.system_properties.created_at, p.id), reverse=True
        )
        for i in range(len(actual_plans)):
            expected_plan = sorted_expected_plans[i]
            plan = actual_plans[i]

            assert plan.name_of_plan == expected_plan.name_of_plan
            assert plan.start_time == expected_plan.start_time
            assert plan.interval == expected_plan.interval
            assert plan.last_modified_at == expected_plan.last_modified_at
            assert plan.events == expected_plan.events
            assert plan.message == expected_plan.message
            assert plan.is_urgent == expected_plan.is_urgent
            assert plan.system_properties.created_at == expected_plan.system_properties.created_at
            assert plan.metadata.archived_at == expected_plan.metadata.archived_at

    async def test_load_temp_plan_should_pass(
        self, user_factory: UserFactoryCallable, temp_plan_repo: TempPlanRepository
    ):
        # Arrange
        user: MemberUser = await user_factory()
        load_plan_request = LoadTempPlanAPIRequestInputBuilder().build()
        body_dict = json.loads(load_plan_request.model_dump_json(by_alias=True))
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}

        # Act
        response = await _call_post_endpoint(request_url=PlanEndpointUrls.TEMP, json=body_dict, headers=headers)

        # Assert
        assert response.status_code == status.HTTP_200_OK
        response_model = CommonDocumentsResponse[TempPlanAPIOutput](**response.json())
        assert len(response_model.documents) == len(load_plan_request.documents)
        for i in range(len(response_model.documents)):
            expected_plan = load_plan_request.documents[i]
            plan = response_model.documents[i]

            assert plan.name_of_plan == expected_plan.name_of_plan
            assert plan.start_time == expected_plan.start_time
            assert plan.interval == expected_plan.interval
            assert plan.last_modified_at == expected_plan.last_modified_at
            assert plan.events == expected_plan.events
            assert plan.message == expected_plan.message
            assert plan.is_urgent == expected_plan.is_urgent
            assert TestUtils.is_date_within_one_minute(plan.system_properties.created_at)
            assert plan.metadata.archived_at is None

        # Teardown
        await temp_plan_repo.delete_by_id([p.id for p in response_model.documents])

    async def test_load_duplicate_temp_plan_should_raise_bad_request(self, user_with_temp_plans):
        # Arrange
        plans, user = user_with_temp_plans
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}
        request_url = join_as_url(base_url=PlanEndpointUrls.TEMP, query_params={"plan_ids": [p.id for p in plans]})

        # Act
        response = await _call_post_endpoint(
            request_url=request_url,
            json=json.loads(
                LoadTempPlanAPIRequestInput(documents=LoadTempPlanInput.multi_map(models=plans)).model_dump_json()
            ),
            headers=headers,
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_400_BAD_REQUEST
