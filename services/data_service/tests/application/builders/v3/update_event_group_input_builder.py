import random
from uuid import uuid4

from services.base.application.generators.custom_models_generators import CustomModelsGenerator
from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.event_group import EventGroupIdentifier
from services.data_service.application.use_cases.events.models.update_event_group_input import UpdateEventGroupInput
from services.data_service.tests.application.builders.v3.update_event_input_builder_base import (
    UpdateEventInputBuilderBase,
)


class UpdateEventGroupInputBuilder(UpdateEventInputBuilderBase, EventGroupIdentifier):

    def __init__(self):
        super().__init__()

    def build(self) -> UpdateEventGroupInput:
        if not self._timestamp and random.choice([True, False]):
            time_interval = CustomModelsGenerator.generate_random_time_interval()
            self._timestamp = time_interval.timestamp
            self._end_time = time_interval.end_time

        return UpdateEventGroupInput(
            id=self._id or uuid4(),
            type=DataType.EventGroup,
            name=PrimitiveTypesGenerator.generate_random_string(max_length=32),
            timestamp=self._timestamp or PrimitiveTypesGenerator.generate_random_aware_datetime(),
            end_time=self._end_time,
            note=PrimitiveTypesGenerator.generate_random_string(allow_none=True),
            tags=PrimitiveTypesGenerator.generate_random_tags(),
            plan_extension=None,
        )
