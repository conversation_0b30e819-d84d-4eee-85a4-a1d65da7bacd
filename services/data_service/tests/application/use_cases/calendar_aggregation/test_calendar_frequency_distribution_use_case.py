import logging
import random
from datetime import datetime, timedelta, timezone
from typing import Awaitable, Callable, Sequence

import pytest
from skyfield.almanac import moon_phases
from skyfield.api import load
from skyfield.searchlib import find_discrete

from services.base.application.boundaries.time_input import TimeRangeInput
from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.repository.event_repository import EventRepository
from services.base.domain.schemas.events.feeling.emotion import Emotion
from services.base.domain.schemas.events.feeling.stress import Stress
from services.base.domain.schemas.member_user.member_user import MemberUser
from services.base.domain.schemas.query.aggregations import CalendarAggregationType
from services.base.domain.schemas.query.builders.boolean_query_builder import BooleanQueryBuilder
from services.base.domain.schemas.query.builders.common_leaf_queries import CommonLeafQueries
from services.base.domain.schemas.query.query import Query
from services.base.domain.schemas.query.type_query import TypeQuery
from services.base.tests.domain.builders.emotion_builder import EmotionBuilder
from services.base.tests.domain.builders.stress_builder import StressBuilder
from services.data_service.application.use_cases.calendar_aggregation.calendar_frequency_distribution_use_case import (
    CalendarFrequencyDistributionUseCase,
)
from services.data_service.constants import EPHEMERIS_PATH


class TestCalendarFrequencyDistributionUseCase:
    @pytest.fixture
    async def user_with_monday_ratings(
        self, event_repo: EventRepository, user_factory: Callable[[], Awaitable[MemberUser]]
    ) -> (MemberUser, int):
        lte_range = datetime.now(tz=timezone.utc).replace(hour=0, minute=0, second=0, microsecond=0)
        gte_range = lte_range - timedelta(days=90)
        expected_documents_found = 10

        user = await user_factory()

        def generate_monday_within_range(start, end):
            random_dt = PrimitiveTypesGenerator.generate_random_aware_datetime(gte=start, lte=end).replace(
                hour=12, minute=0, second=0, microsecond=0
            )

            days_until_monday = (7 - random_dt.weekday()) % 7
            monday_dt = random_dt + timedelta(days=days_until_monday)

            if monday_dt > end:
                monday_dt = monday_dt - timedelta(days=7)

            if monday_dt < start:
                monday_dt = monday_dt + timedelta(days=7)

            return monday_dt

        emotions = [
            EmotionBuilder()
            .with_owner_id(owner_id=user.user_uuid)
            .with_timestamp(generate_monday_within_range(gte_range, lte_range))
            .build()
            for _ in range(expected_documents_found)
        ]
        stresses = [
            StressBuilder()
            .with_owner_id(owner_id=user.user_uuid)
            .with_timestamp(generate_monday_within_range(gte_range, lte_range))
            .build()
            for _ in range(expected_documents_found)
        ]
        emotions = await event_repo.insert(events=emotions, force_strong_consistency=True)
        stresses = await event_repo.insert(events=stresses, force_strong_consistency=True)

        yield user, expected_documents_found * 2

        await event_repo.delete_by_id(ids=[e.id for e in [*emotions, *stresses]])

    @pytest.fixture
    async def user_with_hours_in_day_ratings(
        self, event_repo: EventRepository, user_factory: Callable[[], Awaitable[MemberUser]]
    ) -> (MemberUser, int):
        lte_range = datetime.now(tz=timezone.utc).replace(hour=0, minute=0, second=0, microsecond=0)
        gte_range = lte_range - timedelta(days=90)
        expected_documents_found = 10

        user = await user_factory()
        specific_hours = [2, 14, 20]  # Specific hours to test
        emotions = [
            EmotionBuilder()
            .with_owner_id(owner_id=user.user_uuid)
            .with_timestamp(
                PrimitiveTypesGenerator.generate_random_aware_datetime(gte=gte_range, lte=lte_range).replace(
                    hour=random.choice(specific_hours), minute=0, second=0, microsecond=0
                )
            )
            .build()
            for _ in range(expected_documents_found)
        ]
        stresses = [
            StressBuilder()
            .with_owner_id(owner_id=user.user_uuid)
            .with_timestamp(
                PrimitiveTypesGenerator.generate_random_aware_datetime(gte=gte_range, lte=lte_range).replace(
                    hour=random.choice(specific_hours), minute=0, second=0, microsecond=0
                )
            )
            .build()
            for _ in range(expected_documents_found)
        ]
        emotions = await event_repo.insert(events=emotions, force_strong_consistency=True)
        stresses = await event_repo.insert(events=stresses, force_strong_consistency=True)

        yield user, expected_documents_found * 2

        await event_repo.delete_by_id(ids=[e.id for e in [*emotions, *stresses]])

    @pytest.fixture
    async def user_with_parts_of_month_ratings(
        self, event_repo: EventRepository, user_factory: Callable[[], Awaitable[MemberUser]]
    ) -> (MemberUser, int):
        lte_range = datetime.now(tz=timezone.utc).replace(hour=0, minute=0, second=0, microsecond=0) - timedelta(
            days=30
        )
        gte_range = lte_range - timedelta(days=90)
        expected_documents_found = 10

        user = await user_factory()
        emotions = [
            EmotionBuilder()
            .with_owner_id(owner_id=user.user_uuid)
            .with_timestamp(
                PrimitiveTypesGenerator.generate_random_aware_datetime(gte=gte_range, lte=lte_range).replace(
                    day=random.randint(1, 31 // 5)
                )
            )
            .build()
            for _ in range(expected_documents_found)
        ]
        stresses = [
            StressBuilder()
            .with_owner_id(owner_id=user.user_uuid)
            .with_timestamp(
                PrimitiveTypesGenerator.generate_random_aware_datetime(gte=gte_range, lte=lte_range).replace(
                    day=random.randint(1, 31 // 5)
                )
            )
            .build()
            for _ in range(expected_documents_found)
        ]
        emotions = await event_repo.insert(events=emotions, force_strong_consistency=True)
        stresses = await event_repo.insert(events=stresses, force_strong_consistency=True)

        yield user, expected_documents_found * 2

        await event_repo.delete_by_id(ids=[e.id for e in [*emotions, *stresses]])

    @pytest.fixture
    async def user_with_days_of_month_ratings(
        self, event_repo: EventRepository, user_factory: Callable[[], Awaitable[MemberUser]]
    ) -> (MemberUser, int):
        lte_range = datetime.now(tz=timezone.utc).replace(hour=0, minute=0, second=0, microsecond=0) - timedelta(days=7)
        gte_range = lte_range - timedelta(days=90)
        expected_documents_found = 10

        user = await user_factory()
        specific_days = [1, 15, 28]

        emotions = []
        stresses = []
        for _ in range(expected_documents_found):
            while True:
                random_date = PrimitiveTypesGenerator.generate_random_aware_datetime(
                    gte=gte_range, lte=lte_range
                ).replace(hour=12)

                day = random.choice(specific_days)

                adjusted_date = random_date.replace(day=day)

                if lte_range >= adjusted_date >= gte_range:
                    break

            emotion = EmotionBuilder().with_owner_id(owner_id=user.user_uuid).with_timestamp(adjusted_date).build()
            stress = StressBuilder().with_owner_id(owner_id=user.user_uuid).with_timestamp(adjusted_date).build()
            emotions.append(emotion)
            stresses.append(stress)

        emotions = await event_repo.insert(events=emotions, force_strong_consistency=True)
        stresses = await event_repo.insert(events=stresses, force_strong_consistency=True)

        yield user, expected_documents_found * 2

        await event_repo.delete_by_id(ids=[e.id for e in [*emotions, *stresses]])

    @pytest.fixture
    async def user_with_month_names_ratings(
        self, event_repo: EventRepository, user_factory: Callable[[], Awaitable[MemberUser]]
    ) -> (MemberUser, int):
        lte_range = datetime(year=2024, month=12, day=31, tzinfo=timezone.utc)
        gte_range = datetime(year=2024, month=1, day=1, tzinfo=timezone.utc)
        expected_documents_found = 10

        user = await user_factory()
        specific_months = [1, 4, 7]  # Specific months to test (e.g., January, April, July)
        emotions = [
            EmotionBuilder()
            .with_owner_id(owner_id=user.user_uuid)
            .with_timestamp(
                PrimitiveTypesGenerator.generate_random_aware_datetime(gte=gte_range, lte=lte_range)
                .replace(day=random.randint(1, 28))
                .replace(month=random.choice(specific_months))
            )
            .build()
            for _ in range(expected_documents_found)
        ]
        stresses = [
            StressBuilder()
            .with_owner_id(owner_id=user.user_uuid)
            .with_timestamp(
                PrimitiveTypesGenerator.generate_random_aware_datetime(gte=gte_range, lte=lte_range)
                .replace(day=random.randint(1, 28))
                .replace(month=random.choice(specific_months))
            )
            .build()
            for _ in range(expected_documents_found)
        ]
        emotions = await event_repo.insert(events=emotions, force_strong_consistency=True)
        stresses = await event_repo.insert(events=stresses, force_strong_consistency=True)

        yield user, expected_documents_found * 2

        await event_repo.delete_by_id(ids=[e.id for e in [*emotions, *stresses]])

    @staticmethod
    def get_full_moon_dates(gte_range: datetime, lte_range: datetime, ephemeris_path: str) -> Sequence[datetime]:
        try:
            # Load the pre-downloaded ephemeris file
            eph = load(ephemeris_path)
            ts = load.timescale()

            t0 = ts.utc(gte_range.year, gte_range.month, gte_range.day)
            t1 = ts.utc(lte_range.year, lte_range.month, lte_range.day)

            # Find discrete times and their phases
            times, phases = find_discrete(t0, t1, moon_phases(eph))

            # Extract full moon dates (phase index for Full Moon is 2)
            full_moon_dates = [
                times[i].utc_datetime().replace(tzinfo=timezone.utc) for i, phase in enumerate(phases) if phase == 2
            ]

            return full_moon_dates

        except Exception as e:
            logging.exception(f"Error loading ephemeris file or calculating full moon dates: {e}")
            return []

    @pytest.fixture
    async def user_with_full_moon_ratings(
        self, event_repo: EventRepository, user_factory: Callable[[], Awaitable[MemberUser]]
    ) -> (MemberUser, int):
        lte_range = datetime.now(tz=timezone.utc).replace(hour=0, minute=0, second=0, microsecond=0)
        gte_range = lte_range - timedelta(days=90)

        # Generate full moon dates within the last 90 days
        full_moon_dates = self.get_full_moon_dates(
            gte_range=gte_range,
            lte_range=lte_range,
            ephemeris_path=EPHEMERIS_PATH,
        )
        expected_documents_found = len(full_moon_dates)

        user = await user_factory()

        emotions = [
            EmotionBuilder().with_owner_id(owner_id=user.user_uuid).with_timestamp(full_moon_date).build()
            for full_moon_date in full_moon_dates
        ]
        stresses = [
            StressBuilder().with_owner_id(owner_id=user.user_uuid).with_timestamp(full_moon_date).build()
            for full_moon_date in full_moon_dates
        ]
        emotions = await event_repo.insert(events=emotions, force_strong_consistency=True)
        stresses = await event_repo.insert(events=stresses, force_strong_consistency=True)

        yield user, expected_documents_found * 2

        await event_repo.delete_by_id(ids=[e.id for e in [*emotions, *stresses]])

    @pytest.fixture
    async def user_with_timed_documents(
        self, event_repo: EventRepository, user_factory: Callable[[], Awaitable[MemberUser]]
    ) -> (MemberUser, list[timedelta]):
        base_time = datetime.now(tz=timezone.utc).replace(hour=0, minute=0, second=0, microsecond=0) - timedelta(
            days=25
        )

        time_deltas = [
            timedelta(minutes=15),
            timedelta(hours=1),
            timedelta(hours=5),
            timedelta(days=1),
            timedelta(days=2),
        ]

        actual_time_deltas = [random.choice(time_deltas) for _ in range(10)]
        timestamps = [base_time + delta for delta in actual_time_deltas]

        user = await user_factory()

        emotions = [
            EmotionBuilder().with_owner_id(owner_id=user.user_uuid).with_timestamp(timestamp).build()
            for timestamp in timestamps
        ]
        stresses = [
            StressBuilder().with_owner_id(owner_id=user.user_uuid).with_timestamp(timestamp).build()
            for timestamp in timestamps
        ]
        emotions = await event_repo.insert(events=emotions, force_strong_consistency=True)
        stresses = await event_repo.insert(events=stresses, force_strong_consistency=True)

        yield user, actual_time_deltas

        await event_repo.delete_by_id(ids=[e.id for e in [*emotions, *stresses]])

    async def test_calendar_frequency_distribution_use_case_monday_documents(
        self, dependency_bootstrapper, user_with_monday_ratings
    ):
        user, expected_documents_found = user_with_monday_ratings
        use_case = dependency_bootstrapper.get(CalendarFrequencyDistributionUseCase)
        now = datetime.now(tz=timezone.utc).replace(hour=0, minute=0, second=0, microsecond=0)
        time_input = TimeRangeInput(time_gte=now - timedelta(days=365), time_lte=now)
        and_query = (
            BooleanQueryBuilder()
            .add_query(query=CommonLeafQueries.owner_id_value_query(user_uuid=user.user_uuid))
            .add_query(query=CommonLeafQueries.timestamp_range_query(lte=time_input.time_lte, gte=time_input.time_gte))
        ).build_and_query()

        query = Query(type_queries=[TypeQuery(domain_types=[Emotion, Stress], query=and_query)])
        result = await use_case.execute_async(
            user_uuid=user.user_uuid,
            time_input=time_input,
            query=query,
            calendar_aggregation_type=CalendarAggregationType.WEEKDAYS,
        )
        assert result.results[0].document_count == expected_documents_found

    async def test_calendar_frequency_distribution_use_case_specific_hours_documents(
        self, dependency_bootstrapper, user_with_hours_in_day_ratings
    ):
        user, expected_documents_found = user_with_hours_in_day_ratings
        use_case = dependency_bootstrapper.get(CalendarFrequencyDistributionUseCase)
        now = datetime.now(tz=timezone.utc).replace(hour=0, minute=0, second=0, microsecond=0)
        time_input = TimeRangeInput(time_gte=now - timedelta(days=365), time_lte=now)
        and_query = (
            BooleanQueryBuilder()
            .add_query(query=CommonLeafQueries.owner_id_value_query(user_uuid=user.user_uuid))
            .add_query(query=CommonLeafQueries.timestamp_range_query(lte=time_input.time_lte, gte=time_input.time_gte))
        ).build_and_query()

        query = Query(type_queries=[TypeQuery(domain_types=[Emotion, Stress], query=and_query)])
        result = await use_case.execute_async(
            user_uuid=user.user_uuid,
            time_input=time_input,
            query=query,
            calendar_aggregation_type=CalendarAggregationType.HOURS_IN_DAY,
        )
        total_documents_found = 0
        aggregation_result = result.results
        for result in aggregation_result:
            if result.aggregation_key in ["2", "14", "20"]:
                total_documents_found += result.document_count
        assert total_documents_found == expected_documents_found

    async def test_calendar_frequency_distribution_use_case_specific_month_parts_documents(
        self, dependency_bootstrapper, user_with_parts_of_month_ratings
    ):
        user, expected_documents_found = user_with_parts_of_month_ratings
        use_case = dependency_bootstrapper.get(CalendarFrequencyDistributionUseCase)
        now = datetime.now(tz=timezone.utc).replace(hour=0, minute=0, second=0, microsecond=0)
        time_input = TimeRangeInput(time_gte=now - timedelta(days=365), time_lte=now)
        and_query = (
            BooleanQueryBuilder()
            .add_query(query=CommonLeafQueries.owner_id_value_query(user_uuid=user.user_uuid))
            .add_query(query=CommonLeafQueries.timestamp_range_query(lte=time_input.time_lte, gte=time_input.time_gte))
        ).build_and_query()

        query = Query(type_queries=[TypeQuery(domain_types=[Emotion, Stress], query=and_query)])
        result = await use_case.execute_async(
            user_uuid=user.user_uuid,
            time_input=time_input,
            query=query,
            calendar_aggregation_type=CalendarAggregationType.PARTS_OF_MONTH,
        )
        total_documents_found = 0
        aggregation_result = result.results
        for result in aggregation_result:
            if result.aggregation_key in ["1"]:
                total_documents_found += result.document_count
        assert total_documents_found == expected_documents_found

    async def test_calendar_frequency_distribution_use_case_specific_days_of_month_documents(
        self, dependency_bootstrapper, user_with_days_of_month_ratings
    ):
        user, expected_documents_found = user_with_days_of_month_ratings
        use_case = dependency_bootstrapper.get(CalendarFrequencyDistributionUseCase)
        now = datetime.now(tz=timezone.utc).replace(hour=0, minute=0, second=0, microsecond=0)
        time_input = TimeRangeInput(time_gte=now - timedelta(days=365), time_lte=now)
        and_query = (
            BooleanQueryBuilder()
            .add_query(query=CommonLeafQueries.owner_id_value_query(user_uuid=user.user_uuid))
            .add_query(query=CommonLeafQueries.timestamp_range_query(lte=time_input.time_lte, gte=time_input.time_gte))
        ).build_and_query()

        query = Query(type_queries=[TypeQuery(domain_types=[Emotion, Stress], query=and_query)])
        result = await use_case.execute_async(
            user_uuid=user.user_uuid,
            time_input=time_input,
            query=query,
            calendar_aggregation_type=CalendarAggregationType.DAYS_OF_MONTH,
        )
        total_documents_found = 0
        aggregation_result = result.results
        for result in aggregation_result:
            if result.aggregation_key in ["1", "15", "28"]:
                total_documents_found += result.document_count
        assert total_documents_found == expected_documents_found

    async def test_calendar_frequency_distribution_use_case_specific_months_documents(
        self, dependency_bootstrapper, user_with_month_names_ratings
    ):
        user, expected_documents_found = user_with_month_names_ratings
        use_case = dependency_bootstrapper.get(CalendarFrequencyDistributionUseCase)
        time_input = TimeRangeInput(
            time_gte=datetime(year=2024, month=1, day=1, tzinfo=timezone.utc),
            time_lte=datetime(year=2024, month=12, day=31, tzinfo=timezone.utc),
        )
        and_query = (
            BooleanQueryBuilder()
            .add_query(query=CommonLeafQueries.owner_id_value_query(user_uuid=user.user_uuid))
            .add_query(query=CommonLeafQueries.timestamp_range_query(lte=time_input.time_lte, gte=time_input.time_gte))
        ).build_and_query()

        query = Query(type_queries=[TypeQuery(domain_types=[Emotion, Stress], query=and_query)])
        result = await use_case.execute_async(
            user_uuid=user.user_uuid,
            time_input=time_input,
            query=query,
            calendar_aggregation_type=CalendarAggregationType.MONTH_NAMES,
        )
        total_documents_found = 0
        aggregation_result = result.results
        for result in aggregation_result:
            if result.aggregation_key in ["January", "April", "July"]:
                total_documents_found += result.document_count
        assert total_documents_found == expected_documents_found

    async def test_calendar_frequency_distribution_use_case_full_moon_documents(
        self, dependency_bootstrapper, user_with_full_moon_ratings
    ):
        user, expected_documents_found = user_with_full_moon_ratings
        use_case = dependency_bootstrapper.get(CalendarFrequencyDistributionUseCase)
        now = datetime.now(tz=timezone.utc).replace(hour=0, minute=0, second=0, microsecond=0)
        time_input = TimeRangeInput(time_gte=now - timedelta(days=365), time_lte=now)
        and_query = (
            BooleanQueryBuilder()
            .add_query(query=CommonLeafQueries.owner_id_value_query(user_uuid=user.user_uuid))
            .add_query(query=CommonLeafQueries.timestamp_range_query(lte=time_input.time_lte, gte=time_input.time_gte))
        ).build_and_query()

        query = Query(type_queries=[TypeQuery(domain_types=[Emotion, Stress], query=and_query)])
        result = await use_case.execute_async(
            user_uuid=user.user_uuid,
            time_input=time_input,
            query=query,
            calendar_aggregation_type=CalendarAggregationType.LUNAR_PHASES,
        )
        total_documents_found = 0
        aggregation_result = result.results
        for result in aggregation_result:
            if result.aggregation_key in ["Full Moon"]:
                total_documents_found += result.document_count
        assert total_documents_found == expected_documents_found

    async def test_calendar_frequency_distribution_use_case_time_between_documents(
        self, dependency_bootstrapper, user_with_timed_documents
    ):
        user, time_deltas = user_with_timed_documents
        use_case = dependency_bootstrapper.get(CalendarFrequencyDistributionUseCase)
        now = datetime.now(tz=timezone.utc).replace(hour=0, minute=0, second=0, microsecond=0)
        time_input = TimeRangeInput(time_gte=now - timedelta(days=365), time_lte=now)
        and_query = (
            BooleanQueryBuilder()
            .add_query(query=CommonLeafQueries.owner_id_value_query(user_uuid=user.user_uuid))
            .add_query(query=CommonLeafQueries.timestamp_range_query(lte=time_input.time_lte, gte=time_input.time_gte))
        ).build_and_query()

        query = Query(type_queries=[TypeQuery(domain_types=[Emotion, Stress], query=and_query)])
        result = await use_case.execute_async(
            user_uuid=user.user_uuid,
            time_input=time_input,
            query=query,
            calendar_aggregation_type=CalendarAggregationType.TIME_BETWEEN,
        )
        aggregation_result = result.results
        assert len(aggregation_result) == 5

    async def test_calendar_frequency_distribution_use_case_specific_months_documents_does_fill_null_values(
        self, dependency_bootstrapper, user_with_month_names_ratings
    ):
        user, _ = user_with_month_names_ratings
        use_case = dependency_bootstrapper.get(CalendarFrequencyDistributionUseCase)
        time_input = TimeRangeInput(
            time_gte=datetime(year=2024, month=1, day=1, tzinfo=timezone.utc),
            time_lte=datetime(year=2024, month=12, day=31, tzinfo=timezone.utc),
        )
        and_query = (
            BooleanQueryBuilder()
            .add_query(query=CommonLeafQueries.owner_id_value_query(user_uuid=user.user_uuid))
            .add_query(query=CommonLeafQueries.timestamp_range_query(lte=time_input.time_lte, gte=time_input.time_gte))
        ).build_and_query()

        query = Query(type_queries=[TypeQuery(domain_types=[Emotion, Stress], query=and_query)])
        result = await use_case.execute_async(
            user_uuid=user.user_uuid,
            time_input=time_input,
            query=query,
            calendar_aggregation_type=CalendarAggregationType.MONTH_NAMES,
            fill_null_values=False,
        )
        aggregation_result = result.results
        fetched_keys = []
        for result in aggregation_result:
            fetched_keys.append(result.aggregation_key)

        assert fetched_keys == ["January", "April", "July"]
