from datetime import datetime, time, timedelta, timezone
from typing import Any, List

import pytest

from services.base.application.builders.input_time_interval_builder import InputTimeIntervalBuilder
from services.data_service.application.use_cases.loading.day_splitter import DaySplitter


@pytest.mark.parametrize(
    "input,time_limit",
    (
        (
            [
                InputTimeIntervalBuilder()
                .with_timestamp(timestamp=datetime.now(timezone.utc).replace(microsecond=0) + timedelta(seconds=i))
                .with_end_time(end_time=datetime.now(timezone.utc).replace(microsecond=0) + timedelta(seconds=i + 1))
                .build()
                for i in range(0, 100)
            ],
            time(hour=10),
        ),
        (
            [
                InputTimeIntervalBuilder()
                .with_timestamp(timestamp=datetime.now(timezone.utc).replace(microsecond=0) + timedelta(hours=i))
                .with_end_time(end_time=datetime.now(timezone.utc).replace(microsecond=0) + timedelta(hours=i + 1))
                .build()
                for i in range(0, 100)
            ],
            time(hour=16),
        ),
    ),
)
def test_size_splitter_validate_size_should_pass(input: List[Any], time_limit: time):
    daily_buckets = DaySplitter.split(data=input, time_limit=time_limit)
    for bucket in daily_buckets:
        assert bucket.time_bucket.end_time.hour <= time_limit.hour
        for item in bucket.data:
            assert bucket.time_bucket.timestamp <= item.timestamp <= bucket.time_bucket.end_time
