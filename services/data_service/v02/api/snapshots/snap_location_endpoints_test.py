# -*- coding: utf-8 -*-
# snapshottest: v1 - https://goo.gl/zC4yUc
from __future__ import unicode_literals

from services.data_service.v02.api.snapshot_impl import Snapshot

snapshots = Snapshot()

snapshots["test_location_history_sample_data_fetch_should_pass 1"] = {
    "Status": {
        "NewTimeQueries": {
            "interval": "1y",
            "time_gte": "2019-08-17T06:43:30.000-04:00",
            "time_lte": "2021-08-20T10:43:30.000-04:00",
        }
    },
    "Values": [
        {
            "duration": None,
            "end_time": "2020-01-01T00:00:00.000-04:00",
            "lat": 35.9335403,
            "lon": -78.8859406,
            "timestamp": "2019-01-01T00:00:00.000-04:00",
        },
        {
            "duration": None,
            "end_time": "2021-01-01T00:00:00.000-04:00",
            "lat": 35.9621869,
            "lon": -78.614975,
            "timestamp": "2020-01-01T00:00:00.000-04:00",
        },
        {
            "duration": None,
            "end_time": "2022-01-01T00:00:00.000-04:00",
            "lat": None,
            "lon": None,
            "timestamp": "2021-01-01T00:00:00.000-04:00",
        },
    ],
}

snapshots["test_location_movement_sample_data_fetch_should_pass 1"] = {
    "Status": {
        "NewTimeQueries": {
            "interval": "1y",
            "time_gte": "2019-08-17T06:43:30.000-04:00",
            "time_lte": "2021-08-20T10:43:30.000-04:00",
        }
    },
    "Values": [
        {
            "activities": [
                {"activity_type": "in_passenger_vehicle", "count": 1, "distance": 54885.0, "duration": 2406.0},
                {"activity_type": "still", "count": 1, "distance": 0.0, "duration": 15845.0},
            ],
            "timestamp": "2019-01-01T00:00:00.000-04:00",
        },
        {
            "activities": [
                {"activity_type": "still", "count": 4, "distance": 0.0, "duration": 5506.0},
                {"activity_type": "in_passenger_vehicle", "count": 2, "distance": 25026.0, "duration": 2117.0},
                {"activity_type": "running", "count": 1, "distance": 6159.0, "duration": 2890.0},
                {"activity_type": "walking", "count": 1, "distance": 6304.0, "duration": 4212.0},
            ],
            "timestamp": "2020-01-01T00:00:00.000-04:00",
        },
        {"activities": [], "timestamp": "2021-01-01T00:00:00.000-04:00"},
    ],
}

snapshots["test_location_place_sample_data_fetch_should_pass 1"] = {
    "Status": {
        "NewTimeQueries": {
            "interval": "1y",
            "time_gte": "2019-08-17T06:43:30.000-04:00",
            "time_lte": "2021-08-20T10:43:30.000-04:00",
        }
    },
    "Values": [
        {
            "places": [
                {
                    "address": """123 Shadow Ridge Pl
Chapel Hill, NC 27516
USA""",
                    "coordinates": {"lat": 35.9502118, "lon": -79.1051146},
                    "count": 1,
                    "duration": 15845.0,
                    "name": "123 Shadow Ridge Pl",
                }
            ],
            "timestamp": "2019-01-01T00:00:00.000-04:00",
        },
        {
            "places": [
                {
                    "address": "3008 Royal Forrest Dr, Raleigh, NC 27614-7645, USA",
                    "coordinates": {"lat": 35.9331522, "lon": -78.5677043},
                    "count": 1,
                    "duration": 2439.0,
                    "name": "3008 Royal Forrest Dr",
                },
                {
                    "address": """Falls Lake
North Carolina
USA""",
                    "coordinates": {"lat": 36.027528, "lon": -78.7192776},
                    "count": 1,
                    "duration": 491.0,
                    "name": "Falls Lake",
                },
                {
                    "address": """1432 Brogden Woods Dr Creedmoor Village
Wake Forest, NC 27587
USA""",
                    "coordinates": {"lat": 35.9905, "lon": -78.693269},
                    "count": 1,
                    "duration": 440.0,
                    "name": "Harris Teeter",
                },
                {
                    "address": """1009 Stadium Dr Suite 108
Wake Forest, NC 27587
USA""",
                    "coordinates": {"lat": 35.9894116, "lon": -78.5297284},
                    "count": 1,
                    "duration": 2136.0,
                    "name": "uBreakiFix",
                },
            ],
            "timestamp": "2020-01-01T00:00:00.000-04:00",
        },
        {"places": [], "timestamp": "2021-01-01T00:00:00.000-04:00"},
    ],
}
