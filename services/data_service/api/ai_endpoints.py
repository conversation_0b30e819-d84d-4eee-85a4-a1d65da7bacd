from uuid import UUID

from fastapi import API<PERSON><PERSON><PERSON>, Body, Depends
from fastapi_injector import Injected

from services.base.api.authentication.auth_guard import get_current_uuid
from services.data_service.api.constants import AIEndpointRoutes, DataServicePrefixes
from services.data_service.api.models.request.ai.classify_event_category_request_input import (
    ClassifyEventCategoryRequestInput,
)
from services.data_service.api.models.request.ai.suggest_event_request_input import SuggestEventRequestInput
from services.data_service.application.use_cases.ai.classify_event_category_use_case import (
    ClassifyEventCategoryInputBoundary,
    ClassifyEventCategoryOutput,
    ClassifyEventCategoryUseCase,
)
from services.data_service.application.use_cases.ai.suggest_event_use_case import (
    SuggestEventUseCase,
    SuggestEventUseCaseInputBoundary,
)
from services.data_service.application.use_cases.events.insert_event_inputs import InsertEventInputs

ai_router = APIRouter(
    prefix=f"{DataServicePrefixes.V3}{DataServicePrefixes.AI}",
    tags=["ai"],
    responses={404: {"description": "Not found"}},
)


@ai_router.post(
    AIEndpointRoutes.SUGGEST_EVENT,
    response_model_exclude={"content_hash"},
)
async def suggest_event_endpoint(
    input: SuggestEventRequestInput = Body(...),
    user_uuid: UUID = Depends(get_current_uuid),
    use_case: SuggestEventUseCase = Injected(SuggestEventUseCase),
) -> InsertEventInputs:
    return await use_case.execute(
        user_uuid=user_uuid, input_boundary=SuggestEventUseCaseInputBoundary(query=input.query)
    )


@ai_router.post(AIEndpointRoutes.CLASSIFY_EVENT_CATEGORY)
async def classify_event_category_endpoint(
    input: ClassifyEventCategoryRequestInput = Body(...),
    use_case: ClassifyEventCategoryUseCase = Injected(ClassifyEventCategoryUseCase),
) -> ClassifyEventCategoryOutput:
    return await use_case.execute(input_boundary=ClassifyEventCategoryInputBoundary(query=input.query))
