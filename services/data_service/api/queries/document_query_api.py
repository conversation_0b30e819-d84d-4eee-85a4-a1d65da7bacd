from typing import Sequence

from pydantic import Field

from services.base.api.query.boolean_query_api import CompoundBooleanQueryAPI
from services.base.api.query.leaf_query_api import LeafQueryAPIAnnotated
from services.base.api.query.mapper.query_api_mapper import QueryAPIMapper
from services.base.domain.enums.document_type import DocumentType
from services.base.domain.schemas.query.query import Query
from services.base.domain.schemas.query.type_query import TypeQuery
from services.base.domain.schemas.shared import BaseDataModel

"""
This query variation contains all v3 documents 
"""


class DocumentTypedQueryAPI(BaseDataModel):
    types: Sequence[DocumentType] = Field(min_length=1)
    query: LeafQueryAPIAnnotated | CompoundBooleanQueryAPI | None = Field(default=None)

    def to_type_query(self) -> TypeQuery:
        converted_query = QueryAPIMapper.map(query=self.query)
        return TypeQuery(query=converted_query, domain_types=[dt.to_domain_model() for dt in self.types])


class DocumentQueryAPI(BaseDataModel):
    queries: Sequence[DocumentTypedQueryAPI] = Field(default_factory=list)

    def to_query(self) -> Query:
        if self.queries:
            return Query(type_queries=[q.to_type_query() for q in self.queries])
        else:
            typed_query = TypeQuery(domain_types=[et.to_domain_model() for et in DocumentType], query=None)
            return Query(type_queries=[typed_query])
