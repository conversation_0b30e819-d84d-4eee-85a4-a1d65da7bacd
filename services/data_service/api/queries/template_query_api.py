from typing import Sequence

from pydantic import Field

from services.base.api.query.boolean_query_api import CompoundBooleanQueryAPI
from services.base.api.query.leaf_query_api import LeafQueryAPIAnnotated
from services.base.api.query.mapper.query_api_mapper import QueryAPIMapper
from services.base.domain.schemas.query.query import Query
from services.base.domain.schemas.query.type_query import TypeQuery
from services.base.domain.schemas.shared import BaseDataModel
from services.base.type_resolver import TypeResolver
from services.data_service.api.enums.template_type import TemplateType


class TemplateTypedQueryAPI(BaseDataModel):
    types: Sequence[TemplateType] = Field(min_length=1)
    query: LeafQueryAPIAnnotated | CompoundBooleanQueryAPI | None = Field(default=None)

    def to_type_query(self) -> TypeQuery:
        converted_query = QueryAPIMapper.map(query=self.query)
        domain_types = [TypeResolver.get_document(type_id=t.value) for t in self.types]
        return TypeQuery(query=converted_query, domain_types=domain_types)


class TemplateQueryAPI(BaseDataModel):
    queries: Sequence[TemplateTypedQueryAPI] = Field(default_factory=list)

    def to_query(self) -> Query:
        type_queries = [q.to_type_query() for q in self.queries]
        return Query(type_queries=type_queries)
