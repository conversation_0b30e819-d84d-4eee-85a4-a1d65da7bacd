import pytest

from services.base.domain.constants.document_labels import Document<PERSON>abels
from services.base.domain.enums.event_type import EventType
from services.base.domain.schemas.events.event import EventFields
from services.base.domain.schemas.events.nutrition.nutrition_collection import NutritionFields
from services.base.domain.schemas.events.symptom import SymptomFields
from services.base.domain.schemas.query.aggregations import (
    CalendarAggregationType,
    SimpleAggregationMethod,
)
from services.base.domain.schemas.query.validators.query_validation_exception import QueryValidationException
from services.data_service.api.models.request.aggregate.calendar_aggregations_api_request_input import (
    CalendarHistogramAggregationAPIRequestInput,
)
from services.data_service.api.queries.event_query_api import EventTypedQueryAPI


class TestCalendarHistogramAggregationAPIRequestInput:

    @pytest.mark.parametrize(
        "queries, field_name",
        [
            ([], EventFields.NAME),
            ([EventTypedQueryAPI(types=[EventType.Symptom], query=None)], DocumentLabels.TIMESTAMP),
        ],
    )
    def test_validate_field_type_raises(self, queries, field_name):
        with pytest.raises(QueryValidationException):
            CalendarHistogramAggregationAPIRequestInput(
                queries=queries,
                calendar_aggregation_type=CalendarAggregationType.WEEKDAYS,
                fill_null_values=True,
                field_name=field_name,
                aggregation_method=SimpleAggregationMethod.SUM,
            )

    @pytest.mark.parametrize(
        "queries, field_name",
        [
            ([EventTypedQueryAPI(types=[EventType.Symptom, EventType.Emotion], query=None)], SymptomFields.RATING),
            ([EventTypedQueryAPI(types=[EventType.Food, EventType.Drink], query=None)], NutritionFields.CALORIES),
        ],
    )
    def test_validate_field_type_passes(self, queries, field_name):
        CalendarHistogramAggregationAPIRequestInput(
            queries=queries,
            calendar_aggregation_type=CalendarAggregationType.WEEKDAYS,
            fill_null_values=True,
            field_name=field_name,
            aggregation_method=SimpleAggregationMethod.SUM,
        )
