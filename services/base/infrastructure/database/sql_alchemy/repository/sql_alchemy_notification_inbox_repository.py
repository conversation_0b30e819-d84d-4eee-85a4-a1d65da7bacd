from typing import Callable, Sequence

from sqlalchemy.ext.asyncio import AsyncSession

from services.base.domain.repository.notification_inbox_repository import NotificationInboxRepository
from services.base.domain.repository.wrappers import ReadFromDatabaseWrapper
from services.base.domain.schemas.inbox.member_user_notification_inbox import (
    MemberUserNotificationInbox,
)
from services.base.infrastructure.database.sql_alchemy.models.notification_inbox_entity import (
    NotificationInboxEntity,
)
from services.base.infrastructure.database.sql_alchemy.repository.sql_alch_mapper import SqlAlchMapper
from services.base.infrastructure.database.sql_alchemy.repository.sql_repositories_helper import (
    SqlAlchRepositoriesHelper,
)
from services.base.infrastructure.database.sql_alchemy.sql_alch_async_commons import SqlAlchemyAsyncCommons


class SqlAlchNotificationInboxRepository(NotificationInboxRepository):

    def __init__(self, session_maker: Callable[..., AsyncSession]):
        self._session_maker = session_maker

    async def get(self, wrapper: ReadFromDatabaseWrapper) -> Sequence[MemberUserNotificationInbox]:
        return await SqlAlchRepositoriesHelper.get(
            session_maker=self._session_maker,
            wrapper=wrapper,
            domain_type=MemberUserNotificationInbox,
            entity_type=NotificationInboxEntity,
        )

    async def upsert(
        self, notifications: Sequence[MemberUserNotificationInbox]
    ) -> Sequence[MemberUserNotificationInbox]:
        entities = await SqlAlchemyAsyncCommons.merge(
            session_maker=self._session_maker,
            entities=SqlAlchMapper.to_entity(domain_models=notifications, entity_type=NotificationInboxEntity),
        )
        return SqlAlchMapper.to_domain(entities=entities, domain_type=MemberUserNotificationInbox)

    async def get_notifications_by_uuid(
        self, notification_uuids: Sequence[str]
    ) -> Sequence[MemberUserNotificationInbox]:
        entities = await SqlAlchemyAsyncCommons.get_by_primary_keys(
            session_maker=self._session_maker, entity_type=NotificationInboxEntity, primary_keys=notification_uuids
        )
        return SqlAlchMapper.to_domain(entities=entities, domain_type=MemberUserNotificationInbox)

    async def get_notification_list(self, wrapper: ReadFromDatabaseWrapper) -> Sequence[MemberUserNotificationInbox]:
        query = SqlAlchemyAsyncCommons.build_query_from_wrapper(entity_type=NotificationInboxEntity, wrapper=wrapper)
        entities = await SqlAlchemyAsyncCommons.read_from_databases(
            session_maker=self._session_maker, query=query, entity_type=NotificationInboxEntity
        )
        return SqlAlchMapper.to_domain(entities=entities, domain_type=MemberUserNotificationInbox)

    async def delete(self, notifications: Sequence[MemberUserNotificationInbox]):
        entities = SqlAlchMapper.to_entity(domain_models=notifications, entity_type=NotificationInboxEntity)
        await SqlAlchemyAsyncCommons.delete_from_database(session_maker=self._session_maker, entities=entities)
