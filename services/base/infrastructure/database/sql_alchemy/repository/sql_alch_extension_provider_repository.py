from typing import Callable, Optional, Sequence
from uuid import UUID

from sqlalchemy.ext.asyncio import AsyncSession

from services.base.domain.repository.extension_provider_repository import ExtensionProviderRepository
from services.base.domain.repository.wrappers import ReadFromDatabaseWrapper
from services.base.domain.schemas.extensions.extension_provider import ExtensionProvider
from services.base.infrastructure.database.sql_alchemy.models import ExtensionProviderEntity
from services.base.infrastructure.database.sql_alchemy.repository.sql_alch_mapper import SqlAlchMapper
from services.base.infrastructure.database.sql_alchemy.repository.sql_repositories_helper import (
    SqlAlchRepositoriesHelper,
)
from services.base.infrastructure.database.sql_alchemy.sql_alch_async_commons import SqlAlchemyAsyncCommons


class SqlAlchExtensionProviderRepository(ExtensionProviderRepository):

    def __init__(self, session_maker: Callable[..., AsyncSession]):
        self._session_maker = session_maker

    async def get(self, wrapper: ReadFromDatabaseWrapper) -> Sequence[ExtensionProvider]:
        return await SqlAlchRepositoriesHelper.get(
            session_maker=self._session_maker,
            wrapper=wrapper,
            domain_type=ExtensionProvider,
            entity_type=ExtensionProviderEntity,
        )

    async def delete(self, extension_providers: Sequence[ExtensionProvider]) -> None:
        entities = SqlAlchMapper.to_entity(domain_models=extension_providers, entity_type=ExtensionProviderEntity)
        await SqlAlchemyAsyncCommons.delete_from_database(session_maker=self._session_maker, entities=entities)

    async def get_by_provider_id(self, provider_id: UUID) -> Optional[ExtensionProvider]:
        models = SqlAlchMapper.to_domain(
            entities=await SqlAlchemyAsyncCommons.get_by_primary_keys(
                session_maker=self._session_maker, entity_type=ExtensionProviderEntity, primary_keys=[str(provider_id)]
            ),
            domain_type=ExtensionProvider,
        )
        return next(iter(models), None)

    async def upsert(self, extension_providers: Sequence[ExtensionProvider]) -> Sequence[ExtensionProvider]:
        entities = SqlAlchMapper.to_entity(domain_models=extension_providers, entity_type=ExtensionProviderEntity)
        upserted_entities = await SqlAlchemyAsyncCommons.merge(session_maker=self._session_maker, entities=entities)
        return SqlAlchMapper.to_domain(entities=upserted_entities, domain_type=ExtensionProvider)
