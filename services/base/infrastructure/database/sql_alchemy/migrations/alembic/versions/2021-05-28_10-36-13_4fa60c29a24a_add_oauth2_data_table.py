"""Add oauth2 data table

Revision ID: 4fa60c29a24a
Revises: db8f3683a57f
Create Date: 2021-05-28 10:36:13.455170+00:00

"""

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "4fa60c29a24a"
down_revision = "db8f3683a57f"
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        "user_oauth2_data",
        sa.Column("user_uuid", postgresql.UUID(as_uuid=True), nullable=False, primary_key=True),
        sa.Column("google_refresh_token", sa.Text(), nullable=True),
        sa.Column("google_scope", sa.Text(), nullable=True),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(["user_uuid"], ["member_user.user_uuid"], ondelete="CASCADE"),
    )
    op.create_index(op.f("ix_user_oauth2_data_user_uuid"), "user_oauth2_data", ["user_uuid"], unique=False)
    op.drop_column("login_google", "refresh_token")


def downgrade():
    op.add_column("login_google", sa.Column("refresh_token", sa.TEXT(), autoincrement=False, nullable=True))
    op.drop_index(op.f("ix_user_oauth2_data_user_uuid"), table_name="user_oauth2_data")
    op.drop_table("user_oauth2_data")
