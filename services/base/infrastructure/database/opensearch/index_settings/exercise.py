from typing import Any, Dict

from opensearchpy import Double, Integer

from services.base.domain.schemas.events.activity import ActivityFields
from services.base.domain.schemas.events.exercise.cardio import CardioFields
from services.base.domain.schemas.events.exercise.strength import StrengthFields
from services.base.infrastructure.database.opensearch.index_settings.shared.common import (
    get_base_event_mapping,
)
from services.base.infrastructure.database.opensearch.index_settings.shared.default_index_settings import (
    get_default_event_index_rollover_conditions,
    get_default_event_index_settings,
)
from services.base.infrastructure.database.opensearch.index_settings.shared.utils import convert_dsl_mapping_to_dict
from services.base.infrastructure.database.opensearch.opensearch_index_constants import (
    EXERCISE_INDEX,
    OpenSearchIndex,
)

exercise_mapping = {
    ActivityFields.RATING: Integer(),
    CardioFields.DISTANCE: Double(),
    CardioFields.ELEVATION: Double(),
    StrengthFields.WEIGHT: Double(),
    StrengthFields.COUNT: Integer(),
}


def get_exercise_mapping() -> Dict[str, Any]:

    return convert_dsl_mapping_to_dict(exercise_mapping | get_base_event_mapping(), strict_mapping=True)


def get_event_settings():
    return {
        "default_pipeline": None,
        **get_default_event_index_settings(),
        "plugins.index_state_management.rollover_alias": EXERCISE_INDEX,
    }


ExerciseIndexModel: OpenSearchIndex = OpenSearchIndex(
    name=EXERCISE_INDEX,
    mappings=get_exercise_mapping(),
    settings=get_event_settings(),
    rollover_conditions=get_default_event_index_rollover_conditions(),
    aliases=[EXERCISE_INDEX],
)
