from typing import Any

from opensearchpy import Double, Keyword, Object, Text

from services.base.domain.schemas.events.medication.medication import MedicationFields
from services.base.infrastructure.database.opensearch.index_settings.shared.common import get_base_event_mapping
from services.base.infrastructure.database.opensearch.index_settings.shared.default_index_settings import (
    get_default_event_index_rollover_conditions,
    get_default_event_index_settings,
)
from services.base.infrastructure.database.opensearch.index_settings.shared.utils import convert_dsl_mapping_to_dict
from services.base.infrastructure.database.opensearch.opensearch_index_constants import (
    MEDICATION_INDEX,
    OpenSearchIndex,
)

medication_mapping = {
    MedicationFields.MEDICATION_DETAILS: Object(
        properties={
            MedicationFields.BRAND: Text(),
            MedicationFields.GENERIC_NAME: Text(),
            MedicationFields.RX_CUID: Keyword(),
            MedicationFields.ADMINISTRATION: Keyword(),
        }
    ),
    MedicationFields.SINGLE_DOSE_INFORMATION: Object(
        properties={
            MedicationFields.AMOUNT: Double(),
            MedicationFields.AMOUNT_UNIT: Keyword(),
            MedicationFields.ITEMS_QUANTITY: Double(),
        }
    ),
    MedicationFields.CONSUMED_AMOUNT: Double(),
    MedicationFields.CONSUME_UNIT: Keyword(),
}


def get_medication_mapping() -> dict[str, Any]:

    return convert_dsl_mapping_to_dict(medication_mapping | get_base_event_mapping(), strict_mapping=True)


def get_medication_settings():
    return {
        "default_pipeline": None,
        **get_default_event_index_settings(),
        "plugins.index_state_management.rollover_alias": MEDICATION_INDEX,
    }


MedicationIndexModel: OpenSearchIndex = OpenSearchIndex(
    name=MEDICATION_INDEX,
    mappings=get_medication_mapping(),
    settings=get_medication_settings(),
    rollover_conditions=get_default_event_index_rollover_conditions(),
    aliases=[MEDICATION_INDEX],
)
