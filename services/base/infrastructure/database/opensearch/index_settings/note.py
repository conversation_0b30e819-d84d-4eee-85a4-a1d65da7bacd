from typing import Any, Dict

from services.base.infrastructure.database.opensearch.index_settings.shared.common import get_base_event_mapping
from services.base.infrastructure.database.opensearch.index_settings.shared.default_index_settings import (
    get_default_event_index_rollover_conditions,
    get_default_event_index_settings,
)
from services.base.infrastructure.database.opensearch.index_settings.shared.utils import convert_dsl_mapping_to_dict
from services.base.infrastructure.database.opensearch.opensearch_index_constants import (
    NOTE_INDEX,
    OpenSearchIndex,
)

note_mapping = {}


def get_note_mapping() -> Dict[str, Any]:
    return convert_dsl_mapping_to_dict(note_mapping | get_base_event_mapping(), strict_mapping=True)


def get_event_settings():
    return {
        "default_pipeline": None,
        **get_default_event_index_settings(),
        "plugins.index_state_management.rollover_alias": NOTE_INDEX,
    }


NoteIndexModel: OpenSearchIndex = OpenSearchIndex(
    name=NOTE_INDEX,
    mappings=get_note_mapping(),
    settings=get_event_settings(),
    rollover_conditions=get_default_event_index_rollover_conditions(),
    aliases=[NOTE_INDEX],
)
