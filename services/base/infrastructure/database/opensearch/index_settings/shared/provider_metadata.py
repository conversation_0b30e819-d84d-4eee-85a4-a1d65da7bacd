from opensearchpy import Float, Keyword, Object

from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.schemas.provider_metadata import ProviderFields
from services.base.infrastructure.database.opensearch.opensearch_data_labels import (
    OS_LABEL_CATCH_ALL,
)


def get_os_provider_mapping():
    return {
        DocumentLabels.PROVIDER: Object(
            properties={
                ProviderFields.ENTITY: Keyword(copy_to=OS_LABEL_CATCH_ALL),
                ProviderFields.PRODUCT: Keyword(copy_to=OS_LABEL_CATCH_ALL),
                ProviderFields.DEVICE: Keyword(copy_to=OS_LABEL_CATCH_ALL),
                ProviderFields.APPLICATION: Keyword(copy_to=OS_LABEL_CATCH_ALL),
                ProviderFields.DATA_QUALITY: Float(copy_to=OS_LABEL_CATCH_ALL),
                ProviderFields.DATA_INTEGRITY: Float(copy_to=OS_LABEL_CATCH_ALL),
                ProviderFields.ACQUISITION_METHOD: Keyword(copy_to=OS_LABEL_CATCH_ALL),
            }
        )
    }
