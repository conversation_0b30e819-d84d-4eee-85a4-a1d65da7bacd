from typing import Any, Dict

from opensearchpy import Double, Integer, Keyword, Object

from services.base.domain.schemas.events.nutrition.nutrients import NutrientsFields
from services.base.domain.schemas.events.nutrition.nutrition_collection import NutritionFields
from services.base.infrastructure.database.opensearch.index_settings.shared.common import (
    get_base_event_mapping,
)
from services.base.infrastructure.database.opensearch.index_settings.shared.default_index_settings import (
    get_default_event_index_rollover_conditions,
    get_default_event_index_settings,
)
from services.base.infrastructure.database.opensearch.index_settings.shared.utils import convert_dsl_mapping_to_dict
from services.base.infrastructure.database.opensearch.opensearch_data_labels import OS_LABEL_CATCH_ALL
from services.base.infrastructure.database.opensearch.opensearch_index_constants import (
    NUTRITION_INDEX,
    OpenSearchIndex,
)

nutrition_mapping = {
    NutritionFields.CONSUMED_TYPE: Keyword(copy_to=OS_LABEL_CATCH_ALL),
    NutritionFields.RATING: Integer(),
    NutritionFields.CONSUMED_AMOUNT: Double(),
    NutritionFields.BRAND: Keyword(copy_to=OS_LABEL_CATCH_ALL),
    NutritionFields.FLAVOR: Keyword(copy_to=OS_LABEL_CATCH_ALL),
    NutritionFields.CALORIES: Double(),
    NutritionFields.NUTRIENTS: Object(
        properties={
            NutrientsFields.FAT: Double(),
            NutrientsFields.SATURATED_FAT: Double(),
            NutrientsFields.POLYUNSATURATED_FAT: Double(),
            NutrientsFields.MONOUNSATURATED_FAT: Double(),
            NutrientsFields.TRANS_FAT: Double(),
            NutrientsFields.CHOLESTEROL: Double(),
            NutrientsFields.CARBOHYDRATES: Double(),
            NutrientsFields.FIBER: Double(),
            NutrientsFields.SUGAR: Double(),
            NutrientsFields.PROTEIN: Double(),
            NutrientsFields.SODIUM: Double(),
            NutrientsFields.POTASSIUM: Double(),
            NutrientsFields.VITAMIN_A: Double(),
            NutrientsFields.VITAMIN_C: Double(),
            NutrientsFields.IRON: Double(),
            NutrientsFields.CALCIUM: Double(),
            NutrientsFields.BIOTIN: Double(),
            NutrientsFields.CAFFEINE: Double(),
            NutrientsFields.CHLORIDE: Double(),
            NutrientsFields.CHROMIUM: Double(),
            NutrientsFields.COPPER: Double(),
            NutrientsFields.FOLATE: Double(),
            NutrientsFields.IODINE: Double(),
            NutrientsFields.MAGNESIUM: Double(),
            NutrientsFields.MANGANESE: Double(),
            NutrientsFields.MOLYBDENUM: Double(),
            NutrientsFields.NIACIN: Double(),
            NutrientsFields.PANTOTHENIC_ACID: Double(),
            NutrientsFields.PHOSPHORUS: Double(),
            NutrientsFields.RIBOFLAVIN: Double(),
            NutrientsFields.SELENIUM: Double(),
            NutrientsFields.THIAMIN: Double(),
            NutrientsFields.VITAMIN_B6: Double(),
            NutrientsFields.VITAMIN_B12: Double(),
            NutrientsFields.VITAMIN_D: Double(),
            NutrientsFields.VITAMIN_E: Double(),
            NutrientsFields.VITAMIN_K: Double(),
            NutrientsFields.ZINC: Double(),
        }
    ),
}


def get_nutrition_mapping() -> Dict[str, Any]:
    return convert_dsl_mapping_to_dict(nutrition_mapping | get_base_event_mapping(), strict_mapping=True)


def get_nutrition_settings():
    return {
        "default_pipeline": None,
        **get_default_event_index_settings(),
        "plugins.index_state_management.rollover_alias": NUTRITION_INDEX,
    }


NutritionIndexModel: OpenSearchIndex = OpenSearchIndex(
    name=NUTRITION_INDEX,
    mappings=get_nutrition_mapping(),
    settings=get_nutrition_settings(),
    rollover_conditions=get_default_event_index_rollover_conditions(),
    aliases=[NUTRITION_INDEX],
)
