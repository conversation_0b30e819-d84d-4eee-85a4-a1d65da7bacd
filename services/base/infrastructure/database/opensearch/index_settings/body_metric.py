from typing import Any, Dict

from opensearchpy import Double, Keyword

from services.base.domain.schemas.events.body_metric.blood_glucose import Blood<PERSON><PERSON>coseFields
from services.base.domain.schemas.events.body_metric.blood_pressure import BloodPressureFields
from services.base.domain.schemas.events.body_metric.body_metric import BodyMetricFields
from services.base.infrastructure.database.opensearch.index_settings.shared.common import (
    get_base_event_mapping,
)
from services.base.infrastructure.database.opensearch.index_settings.shared.default_index_settings import (
    get_default_event_index_rollover_conditions,
    get_default_event_index_settings,
)
from services.base.infrastructure.database.opensearch.index_settings.shared.utils import convert_dsl_mapping_to_dict
from services.base.infrastructure.database.opensearch.opensearch_data_labels import OS_LABEL_CATCH_ALL
from services.base.infrastructure.database.opensearch.opensearch_index_constants import (
    BODY_METRIC_INDEX,
    OpenSearchIndex,
)

body_metric_mapping = {
    BodyMetricFields.VALUE: Double(),
    BloodPressureFields.DIASTOLIC: Double(),
    BloodPressureFields.SYSTOLIC: Double(),
    BloodGlucoseFields.SPECIMEN_SOURCE: Keyword(copy_to=OS_LABEL_CATCH_ALL),
}


def get_body_metric_mapping() -> Dict[str, Any]:

    return convert_dsl_mapping_to_dict(body_metric_mapping | get_base_event_mapping(), strict_mapping=True)


def get_body_metric_settings():
    return {
        "default_pipeline": None,
        **get_default_event_index_settings(),
        "plugins.index_state_management.rollover_alias": BODY_METRIC_INDEX,
    }


BodyMetricIndexModel: OpenSearchIndex = OpenSearchIndex(
    name=BODY_METRIC_INDEX,
    mappings=get_body_metric_mapping(),
    settings=get_body_metric_settings(),
    rollover_conditions=get_default_event_index_rollover_conditions(),
    aliases=[BODY_METRIC_INDEX],
)
