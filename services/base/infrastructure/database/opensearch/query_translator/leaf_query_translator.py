from datetime import datetime

from services.base.domain.exceptions.should_not_reach_here_exception import ShouldNotReachHereException
from services.base.domain.schemas.query.leaf_query import (
    ExistsQuery,
    LeafQuery,
    MatchType,
    PatternQuery,
    RadiusQuery,
    RangeQuery,
    ValuesQuery,
)


class LeafQueryTranslator:

    @staticmethod
    def translate(leaf_query: LeafQuery) -> dict:
        if isinstance(leaf_query, ValuesQuery):
            if len(leaf_query.values) == 1:
                return {"term": {leaf_query.field_name: leaf_query.values[0]}}
            else:
                return {"terms": {leaf_query.field_name: leaf_query.values}}
        elif isinstance(leaf_query, PatternQuery):
            multi_match_dict = {
                "multi_match": {
                    "fields": leaf_query.field_names,
                    "query": leaf_query.pattern,
                    "operator": leaf_query.operator.value if leaf_query.operator else "or",
                    "type": LeafQueryTranslator._get_multi_match_query_type(multi_match_query=leaf_query),
                }
            }

            if leaf_query.match_type == MatchType.FUZZY:
                multi_match_dict["multi_match"]["fuzziness"] = "AUTO"

            return multi_match_dict
        elif isinstance(leaf_query, RangeQuery):
            range_query_dict = {"range": {leaf_query.field_name: {}}}
            if leaf_query.lte is not None:
                lte = (
                    leaf_query.lte.isoformat(timespec="milliseconds")
                    if isinstance(leaf_query.lte, datetime)
                    else leaf_query.lte
                )
                range_query_dict["range"][leaf_query.field_name].update({"lte": lte})

            if leaf_query.gte is not None:
                gte = (
                    leaf_query.gte.isoformat(timespec="milliseconds")
                    if isinstance(leaf_query.gte, datetime)
                    else leaf_query.gte
                )
                range_query_dict["range"][leaf_query.field_name].update({"gte": gte})
            return range_query_dict
        elif isinstance(leaf_query, ExistsQuery):
            return {"exists": {"field": leaf_query.field_name}}
        elif isinstance(leaf_query, RadiusQuery):
            return {
                "geo_distance": {
                    "distance": leaf_query.radius,
                    leaf_query.field_name: {
                        "lat": leaf_query.latitude,
                        "lon": leaf_query.longitude,
                    },
                }
            }
        else:
            raise ShouldNotReachHereException(f"Unexpected Leaf Query type. Type: {type(leaf_query)}.")

    @staticmethod
    def _get_multi_match_query_type(multi_match_query: PatternQuery) -> str:
        match multi_match_query.match_type:
            case MatchType.FUZZY:
                return "best_fields"
            case MatchType.EXACT:
                return "phrase"
            case MatchType.DEFAULT:
                return "cross_fields"
            case _:
                raise ShouldNotReachHereException("Unexpected Multi Match Query type.")
