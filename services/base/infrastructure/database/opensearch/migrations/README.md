# Opensearch migrations (WIP)

Managing Opensearch migrations can be tricky as one needs to weight in all dynamical templates and pipelines indices might be using.
Internally, the scripts are written in [**painless**](https://www.elastic.co/guide/en/elasticsearch/painless/current/painless-lang-spec.html) scripting language.


## Table of contents

- [Migration runner](#migration-runner)

## Migration runner

The migration runner is merely a prototype at this point, the goal was to create somewhat generic wrapper around standard migration flow.
Internally, it uses [**Typer**](https://github.com/tiangolo/typer) CLI framework.

### How to use
- Create a new migration file under `./scripts`
- In the script namespace, override global `TARGET_DATA_TYPES` list with target data types
- In the script namespace, override global function with the migration logic.

```async def run_migration(data_type: DataType, client: AsyncOpenSearch):```

Run:

    python3 migration_runner.py --help:               to see options
    python3 migration_runner.py list:                 to see available migration scripts
    python3 migration_runner.py migrate --name $script_name: to run the migration
