import asyncio
import logging
from typing import Sequence

from opensearchpy import Async<PERSON><PERSON>Search

from services.base.dependency_bootstrapper import bootstrapper
from services.base.infrastructure.database.opensearch.index_settings.template import TemplateIndexModel
from services.base.infrastructure.database.opensearch.migrations.migration_wrapper import OSMigrationWrapper
from services.base.infrastructure.database.opensearch.opensearch_index_constants import (
    OpenSearchIndex,
)

logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO)


async def reindex_template_documents(client: AsyncOpenSearch, index_model: OpenSearchIndex, dry_run: bool):
    index_name = index_model.name
    count = (await client.count(index=index_name))["count"]

    logging.info(f"Reindexing {count} documents from {index_name} to convert document field from JSON string to object")

    # Create ingest pipeline for JSON parsing
    pipeline_name = "template-document-json-parser"
    pipeline_body = {
        "description": "Parse JSON string in document field to object",
        "processors": [
            {
                "json": {
                    "field": "document",
                    "target_field": "document",
                    "on_failure": [{"set": {"field": "_migration_error", "value": "Failed to parse document JSON"}}],
                }
            }
        ],
    }

    # Create the pipeline
    await client.ingest.put_pipeline(id=pipeline_name, body=pipeline_body)
    logging.info(f"Created ingest pipeline: {pipeline_name}")

    if dry_run:
        dummy_index = f"dummy-{index_model.name}"
        await OSMigrationWrapper._re_create_index_with_mappings(
            index_model=index_model,
            target_index_name=dummy_index,
            client=client,
        )
        await OSMigrationWrapper.reindex_index_to(
            source=index_name,
            destination=dummy_index,
            client=client,
            script=None,
            requests_per_second=None,
            request_timeout=3600,
            pipeline=pipeline_name,
        )

        new_count = (await client.count(index=dummy_index))["count"]
        if count != new_count:
            raise ValueError(f"Count mismatch: {count} != {new_count}")

        logging.info(
            f"[DRY RUN] reindexed {new_count} documents from {index_name} to {dummy_index}\n"
            f"-------------\n"
            f"COUNT_DIFF: {count - new_count}"
        )

        result = await client.indices.delete(index=dummy_index)
        logging.info(f"[DRY RUN] removed index {dummy_index} with result: {result}")
    else:
        await OSMigrationWrapper.reindex_index_through_dummy_index(
            index_name=index_name,
            index_model=index_model,
            client=client,
            script=None,
            pipeline=pipeline_name,
        )

        new_count = (await client.count(index=index_name))["count"]
        logging.info(f"reindexed {new_count} documents\n" f"-------------\n" f"COUNT_DIFF: {count - new_count}")

    await client.ingest.delete_pipeline(id=pipeline_name)
    logging.info(f"Removed pipeline {pipeline_name}")


async def run_migrations(index_models: Sequence[OpenSearchIndex], dry_run: bool):
    client = bootstrapper.get(interface=AsyncOpenSearch)

    for index_model in index_models:
        try:
            await reindex_template_documents(
                client=client,
                index_model=index_model,
                dry_run=dry_run,
            )
        finally:
            await client.close()


if __name__ == "__main__":
    asyncio.run(
        run_migrations(
            index_models=[TemplateIndexModel],
            dry_run=False,
        )
    )
