from uuid import UUID, uuid4

from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.diary_events import DiaryEvents
from services.base.domain.schemas.events.activity import Activity, ActivityCategory
from services.base.domain.schemas.events.document_base import RBACSchema
from services.base.domain.schemas.events.event import EventPlanExtension
from services.base.domain.schemas.temp_plan import TempPlanDiaryEvent
from services.base.domain.schemas.templates.payload.activity_template_payload import ActivityTemplatePayload
from services.base.infrastructure.database.opensearch.migrations.scripts.convertor.convertor_helpers import (
    ConvertorHelpers,
)


class ActivityConvertor:
    def __init__(self, category_mapping: dict[str, dict[str, str]]):
        self._category_mapping = category_mapping[DataType.Activity]

    def event_to_activity(self, diary_event: DiaryEvents, template_id: UUID | None, plan_id: UUID | None) -> Activity:
        metadata = ConvertorHelpers.create_base_metadata(diary_event)
        tags = ConvertorHelpers.get_diary_event_tags(diary_event=diary_event)

        if diary_event.explanation:
            diary_event.explanation = diary_event.explanation.strip()

        return Activity(
            id=uuid4(),
            submission_id=uuid4(),
            name=diary_event.name,
            note=diary_event.explanation if diary_event.explanation else None,
            rbac=RBACSchema(owner_id=diary_event.metadata.user_uuid),
            timestamp=diary_event.timestamp,
            type=DataType.Activity,
            category=ActivityCategory(self._category_mapping[diary_event.name]),
            tags=tags,
            metadata=metadata,
            system_properties=diary_event.system_properties,
            template_id=template_id,
            asset_references=ConvertorHelpers.convert_old_asset_references_to_new_asset_references(
                old_asset_references=diary_event.asset_references
            ),
            group_id=None,
            plan_extension=EventPlanExtension(plan_id=plan_id) if plan_id else None,
            rating=diary_event.intensity,
            end_time=diary_event.end_time,
        )

    def event_to_activity_template(self, diary_event: DiaryEvents | TempPlanDiaryEvent) -> ActivityTemplatePayload:
        tags = ConvertorHelpers.get_diary_event_tags(diary_event=diary_event)
        duration = (diary_event.end_time - diary_event.timestamp).total_seconds() if diary_event.end_time else None

        if diary_event.explanation:
            diary_event.explanation = diary_event.explanation.strip()

        return ActivityTemplatePayload(
            type=DataType.Activity,
            category=ActivityCategory(self._category_mapping[diary_event.name]),
            name=diary_event.name,
            duration=duration,
            note=diary_event.explanation if diary_event.explanation else None,
            tags=tags,
            rating=diary_event.intensity,
        )
