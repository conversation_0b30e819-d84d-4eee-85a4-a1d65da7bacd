import json
import logging
from typing import Any, Optional, Sequence

from pydantic import ValidationError

from services.base.application.boundaries.documents import (
    SearchUniqueDocumentsOutputBoundary,
)
from services.base.application.database.document_search_service import DocumentSearchService
from services.base.application.database.models.filters import Filters
from services.base.application.database.models.sorts import Sort
from services.base.application.utils.serializers import serialize_with_datetime
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.enums.data_types import DataType
from services.base.domain.repository.models.search_results import SearchResults
from services.base.domain.schemas.events.document_base import Document, TagsDocument
from services.base.domain.schemas.query.query import Query
from services.base.domain.schemas.query.single_document_type_query import SingleDocumentTypeQuery
from services.base.domain.schemas.shared import IdentifiableModel
from services.base.infrastructure.database.opensearch.continuation_token_utils import ContinuationTokenUtils
from services.base.infrastructure.database.opensearch.opensearch_aggs_builder import OpenSearchAggsBuilder
from services.base.infrastructure.database.opensearch.opensearch_client import OpenSearchClient
from services.base.infrastructure.database.opensearch.opensearch_index_constants import DIARY_EVENTS_INDEX
from services.base.infrastructure.database.opensearch.opensearch_mappings import (
    OpenSearchIndexToDocumentModelMapping,
)
from services.base.infrastructure.database.opensearch.opensearch_query_builder import OpenSearchQueryBuilder
from services.base.infrastructure.database.opensearch.opensearch_request_builder import OpenSearchRequestBuilder
from services.base.infrastructure.database.opensearch.query_translator.query_translator import QueryTranslator


class OSDocumentSearchService(DocumentSearchService):
    _client: OpenSearchClient

    def __init__(self, client: OpenSearchClient):
        self._client = client

    async def close(self):
        await self._client.close()

    async def search_documents_by_query(
        self,
        sorts: Sequence[Sort],
        query: Query,
        size: int = 1000,
        continuation_token: Optional[str] = None,
    ) -> SearchResults[Document]:
        response = await self._search_by_query(
            sorts=sorts, query=query, size=size, continuation_token=continuation_token
        )
        return self._get_models_from_document_stream_response(response=response, mapping_type=None)

    async def map_documents_to[T: Document](
        self,
        sorts: Sequence[Sort],
        projected_type: type[T],
        query: Query,
        size: int = 1000,
        continuation_token: Optional[str] = None,
    ) -> SearchResults[T]:
        response = await self._search_by_query(
            sorts=sorts, query=query, size=size, continuation_token=continuation_token
        )
        return self._get_models_from_document_stream_response(response=response, mapping_type=projected_type)  # type: ignore

    async def search_documents_by_single_query[T: Document](
        self,
        sorts: Sequence[Sort],
        query: SingleDocumentTypeQuery[T],
        size: int = 1000,
        continuation_token: Optional[str] = None,
    ) -> SearchResults[T]:
        response = await self._search_by_query(
            sorts=sorts, query=query.to_query(), size=size, continuation_token=continuation_token
        )
        return self._get_models_from_document_stream_response(response=response, mapping_type=None)  # type: ignore

    async def search_unique_by_query[T: Document](
        self,
        data_schema: type[T],
        size: int,
        field_name: str,
        filters: Optional[Filters] = None,
        sorts: Optional[Sequence[Sort]] = None,
    ) -> Sequence[SearchUniqueDocumentsOutputBoundary[T]]:
        query_builder = OpenSearchQueryBuilder()
        if filters:
            query_builder.with_filters(filters=filters)

        aggs_builder = OpenSearchAggsBuilder().with_unique_aggregation(field_name=field_name, size=size, sort=sorts)

        request_builder = (
            OpenSearchRequestBuilder().with_query(query=query_builder.build()).with_aggs(aggs=aggs_builder.build())
        )

        request_body = request_builder._request_body
        logging.info(
            f"Searching uniques in data type {data_schema} with query {json.dumps(request_body, default=serialize_with_datetime)}"
        )
        response = await self._client.search_by_query(body=request_body, data_schema=data_schema)
        return self._get_models_from_search_unique_response(data_schema=data_schema, response=response)

    async def count_by_query(self, query: Query) -> int:
        query_result = QueryTranslator.translate(query)
        return await self._client.count(indices=query_result.indices, body=query_result.query_as_dict)

    def _get_models_from_document_stream_response(
        self, response: dict, mapping_type: type[Document] | None
    ) -> SearchResults[Document]:
        output: list[Document] = []
        hits: Sequence[dict[str, Any]] = response["hits"]["hits"]
        continuation_token = None
        if hits:
            for hit in hits:
                source = hit["_source"]
                document_model = mapping_type or self._get_document_model_from_hit(hit=hit)
                if issubclass(document_model, IdentifiableModel):
                    source |= {DocumentLabels.ID: source[DocumentLabels.DOC_ID]}
                else:
                    source |= {DocumentLabels.ID: hit["_id"]}
                if issubclass(document_model, TagsDocument):
                    tags = source[DocumentLabels.TAGS]
                    source |= {DocumentLabels.TAGS: [tag[DocumentLabels.TAG] for tag in tags]}
                try:
                    output.append(document_model(**source))
                except ValidationError as err:
                    logging.error(
                        f"failed to deserialize {document_model.type_id()}",
                        extra={
                            "document_id": hit["_id"],
                            "index": hit["_index"],
                            "source": source,
                            "error": str(err),
                        },
                    )
            sorts = hits[-1]["sort"]
            continuation_token = (
                ContinuationTokenUtils.encode_continuation_token(search_sort_mark=[str(sort) for sort in sorts])
                if sorts
                else None
            )

        return SearchResults[Document](documents=output, continuation_token=continuation_token)

    def _get_document_model_from_hit(self, hit: dict) -> type[Document]:
        index: str = hit["_index"]
        if not index.startswith(DIARY_EVENTS_INDEX):
            type = hit["_source"].get("type")
            if type:
                return DataType(type).to_domain_model()
        return OpenSearchIndexToDocumentModelMapping[index.split("-")[0]]

    async def _search_by_query(
        self,
        sorts: Sequence[Sort],
        query: Query,
        size: int = 1000,
        continuation_token: Optional[str] = None,
    ) -> dict:
        if not sorts:
            raise ValueError("Sort has to be specified for feed query.")

        query_translation_result = QueryTranslator.translate(query=query)

        query_as_dict = query_translation_result.query_as_dict
        indices = query_translation_result.indices

        request_builder = (
            OpenSearchRequestBuilder().with_query_v2(query=query_as_dict).with_size(size=size).with_sorts(sorts=sorts)
        )
        if continuation_token:
            request_builder.with_search_after(
                search_after=ContinuationTokenUtils.decode_continuation_token(continuation_token=continuation_token)
            )

        request_body = request_builder._request_body
        logging.info(
            f"Searching data in indices: {indices}, query: {json.dumps(request_body, default=serialize_with_datetime)}"
        )
        return await self._client.search_indices(body=request_body, indices=indices)

    def _get_models_from_search_unique_response[T](
        self,
        data_schema: type[T],
        response: dict,
    ) -> Sequence[SearchUniqueDocumentsOutputBoundary[T]]:
        results = []
        buckets: Sequence[dict[str, Any]] = response["aggregations"]["aggregation"]["buckets"]
        for bucket in buckets:
            hits = bucket["sub_aggregation"]["hits"]["hits"]
            for hit in hits:
                _id = hit["_id"]
                response = hit["_source"]
                results.append(
                    SearchUniqueDocumentsOutputBoundary(
                        document=data_schema(**response),
                        id=_id,
                    )
                )
        return results
