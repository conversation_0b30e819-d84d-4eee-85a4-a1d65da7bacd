import logging
from typing import Sequence

from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.schemas.inbox.inbox_message import InboxMessageFields
from services.base.domain.schemas.query.validators.query_validation_exception import QueryValidationException

BLACKLISTED_FIELDS = [
    f"{DocumentLabels.METADATA}.{DocumentLabels.USER_UUID}",
    f"{InboxMessageFields.DESTINATION}.{DocumentLabels.ID}",
]


class LeafQueryAPIValidator:

    @staticmethod
    def validate_field_names(field_names: Sequence[str]):
        for field_name in field_names:
            for blacklisted_field in BLACKLISTED_FIELDS:
                if blacklisted_field == field_name:
                    logging.info(f"Client query contained blacklisted field: {field_name}")
                    raise QueryValidationException(f"Invalid query field: {field_name}")
