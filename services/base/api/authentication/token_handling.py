from datetime import datetime, timedelta, timezone
from typing import Optional
from uuid import UUID

from services.base.api.authentication.exceptions import InvalidCredentialsException
from services.base.application.authorization_encryption import decrypt, encrypt
from services.base.application.constants import UserTokenKeys
from services.base.application.jwt_handling import encode_jwt
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.constants.time_constants import SECONDS_IN_DAY
from settings.app_config import settings
from settings.app_secrets import secrets


def generate_access_token(
    user_uuid: UUID, time_delta: timedelta = timedelta(minutes=settings.API_ACCESS_TOKEN_EXPIRATION_TIME_MINUTES)
) -> str:
    """
    Generates jwt access token for app user
    """
    now = datetime.now(timezone.utc)
    access_token_payload = {
        DocumentLabels.USER_UUID: encrypt(string=str(user_uuid)),
        UserTokenKeys.EXPIRATION_TIME: now + time_delta,
        UserTokenKeys.ISSUED_AT: now,
    }
    access_token: str = encode_jwt(payload=access_token_payload, key=secrets.ACCESS_TOKEN_SECRET)
    return access_token


def generate_refresh_token(
    user_uuid: UUID, time_delta: timedelta = timedelta(days=settings.API_REFRESH_TOKEN_EXPIRATION_TIME_DAYS)
) -> str:
    """
    Generates jwt refresh token for app user
    """
    now = datetime.now(timezone.utc)
    refresh_token_payload = {
        DocumentLabels.USER_UUID: encrypt(string=str(user_uuid)),
        UserTokenKeys.EXPIRATION_TIME: now + time_delta,
        UserTokenKeys.ISSUED_AT: now,
    }
    refresh_token: str = encode_jwt(payload=refresh_token_payload, key=secrets.REFRESH_TOKEN_SECRET)

    return refresh_token


def get_uuid_from_token(decoded_token: dict[str, str | int]) -> UUID:
    token_uuid = decoded_token.get(DocumentLabels.USER_UUID)
    if token_uuid is None:
        raise InvalidCredentialsException(message="failed to obtain identity from the token")
    return UUID(decrypt(token=str(token_uuid)))


def rotate_refresh_token(user_id: UUID, refresh_expiration_timestamp: float) -> Optional[str]:
    if (refresh_expiration_timestamp - datetime.now(timezone.utc).timestamp()) < (
        SECONDS_IN_DAY * settings.API_REFRESH_TOKEN_ROTATION_TIME_DAYS
    ):
        return generate_refresh_token(
            user_uuid=user_id, time_delta=timedelta(days=settings.API_REFRESH_TOKEN_EXPIRATION_TIME_DAYS)
        )
