from http import <PERSON><PERSON><PERSON><PERSON><PERSON>
from typing import Dict, Type

from httpx import AsyncClient

from services.base.application.exceptions import (
    BadRequestException,
    DefaultException,
    InternalServerErrorException,
    NoContentException,
)


class HttpClient:
    _client: AsyncClient

    def __init__(self, client: AsyncClient):
        self._client = client

    async def do_request[T](
        self,
        url: str,
        response_model: Type[T],
        method: HTTPMethod = HTTPMethod.GET,
        body: dict | None = None,
        headers: Dict[str, str] | None = None,
        timeout: float | None = None,
    ) -> T:
        """
        A wrapper around an HTTP client. Given a type in `response_model`, the response's body will be serialized into `response_model(**body)` and returned.

        Params:
        ---
        url(str):
            The URL to make the request to. Make sure it follows the whole format of the URL, including the protocol (http/https).

        response_model(T):
            Pydantic model to use to serialize the response into.

        method(http.HTTPMethod):
            One of the supported HTTP methods.

        body(Dict | None):
            Pass in a body to the request.

        headers(Dict[str,str] | None):
            Optional parameter to set headers in the request.

        timeout(float | None):
            Allows setting a timeout of the request. By default it ensures there is no timeout set.
        """
        req = self._client.build_request(method=method.value, url=url, headers=headers, timeout=timeout, json=body)
        response = await self._client.send(req)

        if response.status_code == 200:
            return response_model(**response.json())

        match response.status_code:
            case 204:
                raise NoContentException(message=f"no content received url={url}")
            case 400:
                raise BadRequestException(message=f"invalid request url={url}, body={response.json()}")
            case 500:
                raise InternalServerErrorException(message=f"internal server error url={url} body={response.json()}")
            case _:
                raise DefaultException(message=f"non-successful status code returned url={url} body={response.json()}")
