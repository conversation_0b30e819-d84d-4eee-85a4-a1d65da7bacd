import logging
from abc import ABC, abstractmethod
from datetime import datetime, timed<PERSON>ta
from sys import getsizeof
from typing import List, Optional, Union
from uuid import UUID
from zoneinfo import ZoneInfo

from dateutil.parser import parse
from opensearchpy import OpenSearch

from services.base.application.database.depr_event_repository import DeprEventRepository
from services.base.application.input_validators.shared import InputTimestampModel
from services.base.application.size_constants import SIZE_MB
from services.base.application.utils.time import get_datetime_difference, tz_infos
from services.base.domain.constants.time_constants import DEFAULT_TIMEZONE_UTC
from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.metadata import Organization
from services.base.domain.schemas.shared import BaseDataModel
from services.base.domain.time import parse_datetime_tz_fallback
from services.base.infrastructure.database.opensearch.opensearch_committer import OpenSearchCommitter
from services.base.infrastructure.database.opensearch.wrappers.client import get_default_os_client


class LoaderBase(ABC):
    MAX_BULK_LIST_SIZE_B = 10 * SIZE_MB  # 10MB
    _source = None
    PROVIDER: Organization | None = None

    @abstractmethod
    def __init__(
        self,
        user_uuid: UUID,
        data_type: DataType,
        client: OpenSearch | None = None,
        fallback_timezone: ZoneInfo = DEFAULT_TIMEZONE_UTC,
        event_repo: DeprEventRepository | None = None,
        **kwargs,
    ):
        """
        Args:
            data_type (str|dict): either target index name or a dictionary mapping to indexes for multi read
                                   in case of dict - keys depend on the particular loader implementation
                                   this is meant to be used for loaders that load 1 file to 2+ indexes
                                   Do NOT confuse with loading same file into multiple indexes!
                                   - that is done by runners / running the loaders not by the loaders themselves
        """
        self.data_type = data_type
        self.user_uuid = user_uuid
        self.client = client if client is not None else get_default_os_client()
        self._event_repo = event_repo

        # can't fallback to UTC directly, if there would be a different fallback...
        self.DEFAULT_TIMEZONE: ZoneInfo = fallback_timezone

    @abstractmethod
    def process_data(self) -> None:
        pass

    def _parse_data_to_entry(self, entry_list: List[InputTimestampModel]) -> BaseDataModel:
        raise NotImplementedError("Loader must have data parsing method implemented.")

    def _process_input_data(
        self,
        input_entries: List[InputTimestampModel],
        load_aggregation_interval: Optional[timedelta] = None,
    ) -> None:
        input_entries.sort(
            key=lambda entry: (
                entry.timestamp if isinstance(entry.timestamp, datetime) else parse(entry.timestamp, tzinfos=tz_infos)
            )
        )
        to_be_committed_entries: list[str] = []
        # Defines list of entries to be parsed into single database entry
        bulk_entries: list[InputTimestampModel] = []
        cached_json_entry = None
        for input_entry in input_entries:
            try:
                timestamp = self._parse_tz_datetime(input_entry.timestamp)
                data_entry = input_entry.map(input_entry, fields={"timestamp": timestamp})
                # Copies and appends the starting entry
                if not cached_json_entry:
                    cached_json_entry = data_entry

                # If interval is set, verify bucket size
                if load_aggregation_interval:
                    time_difference = get_datetime_difference(
                        cached_json_entry.timestamp,
                        data_entry.timestamp,
                    )
                    # If bucket is not full yet, append entry and continue the main loop
                    if time_difference < load_aggregation_interval:
                        # Append entry
                        bulk_entries.append(data_entry)
                        continue
                output_data = self._parse_data_to_entry(bulk_entries)
                # Append to be committed entries
                to_be_committed_entries.append(output_data.model_dump_json(by_alias=True))
                # clear entries
                del bulk_entries[:]
                # Sets current data entry as new cached starting one
                cached_json_entry = data_entry
                bulk_entries.append(cached_json_entry)
            except Exception as error:  # pylint:disable=broad-except
                logging.exception("Error processing entry: %s, %s", input_entry, repr(error))
                del bulk_entries[:]
            self._commit_if_limit_reached(to_be_committed_entries)
        if bulk_entries:
            output_data = self._parse_data_to_entry(bulk_entries)
            to_be_committed_entries.append(output_data.model_dump_json(by_alias=True))
        self._commit(entries=to_be_committed_entries)

    @property
    def source(self) -> str:
        """Defines the name of the source product."""
        if self._source is None:
            raise NotImplementedError("Loader must have source name field implemented.")
        return self._source

    @classmethod
    def _should_commit(cls, entries: List[str]) -> bool:
        # 2 important implementation details of sys.getsizeof
        # 1) All built-in objects will return correct results,
        #    but this does not have to hold true for third-party extensions as it is implementation specific.
        # 2) Only the memory consumption directly attributed to the object is accounted for,
        #    NOT the memory consumption of objects it refers to.
        return getsizeof(entries) >= cls.MAX_BULK_LIST_SIZE_B

    def _commit(self, entries: List[str], data_type: Optional[DataType] = None) -> None:
        if len(entries) > 0:
            if data_type is None:
                data_type = self.data_type
            OpenSearchCommitter.bulk_commit(client=self.client, data_type=data_type, entries=entries)
            del entries[:]

    def _commit_if_limit_reached(self, entries: List[str], data_type: Optional[DataType] = None) -> None:
        """FLUSHES entries (if limit is reached and data committed) (does del entries[:])"""
        if self._should_commit(entries=entries):
            self._commit(entries=entries, data_type=data_type)

    # This function is intentionally separate and on the loader level
    # because it contains logic specific to how loaders are handling tz fallbacks
    def _parse_tz_datetime(self, _datetime: Union[datetime, str, None]) -> Union[datetime, None]:
        """Returns "as is" if it's already a timezone-aware datetime.
        If the _datetime doesn't have timezone, fallback is done in this order:
        1. loader's DEFAULT_TIMEZONE
        2. member_user_settings.general.timezone
        3. app_constants => FALLBACK_TIMEZONE_PYTZ
        If it's string => it's first parsed to datetime (and same applies).
        If the _datetime is None => returns None"""
        return parse_datetime_tz_fallback(value=_datetime, fallback_timezone=self.DEFAULT_TIMEZONE)
