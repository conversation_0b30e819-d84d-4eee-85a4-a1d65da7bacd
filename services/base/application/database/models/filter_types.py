from __future__ import annotations

from typing import Sequence

from pydantic import Field, model_validator

from services.base.domain.annotated_types import NonEmptyStr, SerializableAwareDatetime
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.constants.extension_labels.extension_labels import ExtensionLabels
from services.base.domain.schemas.shared import BaseDataModel


class Filter(BaseDataModel): ...


class RangeFilter[T: (int, float, SerializableAwareDatetime)](Filter):
    name: NonEmptyStr
    gte: T | None = Field(default=None, alias=DocumentLabels.GTE)
    lte: T | None = Field(default=None, alias=DocumentLabels.LTE)

    @model_validator(mode="after")
    def validate_range_filter(self):
        if self.gte is None and self.lte is None:
            raise ValueError("lte or gte has to be set")
        if self.gte is not None and self.lte is not None:
            if self.gte > self.lte:
                raise ValueError("gte has to be less than lte")
        return self


class TermsFilter(Filter):
    name: NonEmptyStr
    value: Sequence[str] = Field(min_length=1)


class ExistsFilter(Filter):
    name: NonEmptyStr


class MatchFilter(Filter):
    fields: Sequence[NonEmptyStr]
    value: str
    exact_match: bool = Field(default=False)
    fuzzy_match: bool = Field(default=False)
    operator: NonEmptyStr | None = None

    @model_validator(mode="after")
    def validate_filter_consistency(self):
        if self.exact_match and self.operator:
            raise ValueError("operator must not be used for exact match type queries")
        return self


class OrganizationTermsFilter(TermsFilter):
    name: str = Field(default=DocumentLabels.ORGANIZATION)


class UserUUIDTermsFilter(TermsFilter):
    name: str = Field(default=DocumentLabels.USER_UUID)


class TagsTermsFilter(TermsFilter):
    name: str = Field(default=DocumentLabels.TAGS)


class ExtensionIdTermsFilter(TermsFilter):
    name: str = Field(default=f"{DocumentLabels.METADATA}.{ExtensionLabels.EXTENSION_ID}")


class DocIdTermsFilter(TermsFilter):
    name: str = Field(default=DocumentLabels.DOC_ID)


class TimestampRangeFilter(RangeFilter):
    name: str = Field(default=DocumentLabels.TIMESTAMP)


class CreatedAtRangeFilter(RangeFilter):
    name: str = Field(default=f"{DocumentLabels.SYSTEM_PROPERTIES}.{DocumentLabels.CREATED_AT}")


class UpdatedAtRangeFilter(RangeFilter):
    name: str = Field(default=f"{DocumentLabels.SYSTEM_PROPERTIES}.{DocumentLabels.UPDATED_AT}")
