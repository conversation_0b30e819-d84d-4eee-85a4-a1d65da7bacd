from abc import ABC, abstractmethod
from typing import Sequence

from services.base.application.boundaries.aggregates import DateHistogramAggregate, FrequencyDistributionAggregate
from services.base.domain.schemas.events.document_base import Document
from services.base.domain.schemas.query.aggregations import DateHistogramAggregation
from services.base.domain.schemas.query.query import Query


class AggregationService(ABC):
    @abstractmethod
    def __init__(self, **kwargs):
        pass

    @abstractmethod
    async def close(self):
        pass

    @abstractmethod
    async def date_histogram_by_query(
        self,
        query: Query,
        aggregation: DateHistogramAggregation,
    ) -> Sequence[DateHistogramAggregate]:
        pass

    @abstractmethod
    async def frequency_distribution_by_query(
        self,
        query: Query,
        field_name: str,
        size: int,
    ) -> Sequence[FrequencyDistributionAggregate]:
        """
        Returns frequency distribution of unique values for given field name
        Args:
            size (int): defines the number of unique terms aggregated in the frequency distributio
        """
        pass

    @abstractmethod
    def adjust_frequency_distribution_field(
        self,
        domain_types: Sequence[type[Document]],
        field_name: str,
    ) -> str:
        """
        Validates given field can be used for frequency distribution across domain types requested
        Adjusts field name if database mapping requires it
        """
        pass
