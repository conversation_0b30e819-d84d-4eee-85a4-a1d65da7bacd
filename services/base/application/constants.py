"""Generic cross-service constants"""

from enum import StrEnum

TEXT_UNKNOWN = "Unknown"
# Api authenticaton constants


class UserTokenKeys:
    API_REFRESH_TOKEN = "api_refresh_token"
    API_ACCESS_TOKEN = "api_access_token"
    API_ACCESS_TOKEN_HEADER = "api-access-token"
    ISSUED_AT = "iat"
    EXPIRATION_TIME = "exp"


class JwtAlgorithms(StrEnum):
    HS256 = "HS256"
    RS256 = "RS256"


class OAuth2Keys(StrEnum):
    CLIENT_ID = "client_id"
    CLIENT_SECRET = "client_secret"
    CODE_VERIFIER = "code_verifier"
    CODE_CHALLENGE = "code_challenge"
    CODE_CHALLENGE_METHOD = "code_challenge_method"

    SCOPE = "scope"
    RESPONSE_TYPE = "response_type"
    REDIRECT_URI = "redirect_uri"
    GRANT_TYPE = "grant_type"
    STATE = "state"
    CODE = "code"

    ACCESS_TOKEN = "access_token"
    REFRESH_TOKEN = "refresh_token"
    ID_TOKEN = "id_token"
    USER_ID = "user_id"
