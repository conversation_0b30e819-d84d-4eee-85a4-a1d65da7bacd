from datetime import timedelta

import pytest
from dateutil.relativedelta import relativedelta

from services.base.application.utils.time import TimeUtils


@pytest.mark.parametrize(
    ("timestring", "expected_output"),
    [
        ("1h", relativedelta(hours=1)),
        ("10h", relativedelta(hours=10)),
        ("1d", relativedelta(days=1)),
        ("1D", relativedelta(days=1)),
        ("10d", relativedelta(days=10)),
        ("10D", relativedelta(days=10)),
        ("1w", relativedelta(weeks=1)),
        ("10w", relativedelta(weeks=10)),
        ("1M", relativedelta(months=1)),
        ("10M", relativedelta(months=10)),
        ("1q", relativedelta(months=3)),
        ("10q", relativedelta(months=30)),
        ("1y", relativedelta(years=1)),
        ("10y", relativedelta(years=10)),
    ],
)
def test_get_relativedelta_from_aggregation_interval(timestring: str, expected_output: relativedelta):
    assert TimeUtils.get_relativedelta_from_aggregation_interval(timestring) == expected_output


@pytest.mark.parametrize(
    "timestring",
    ["1B", "1BBBBBB"],
)
def test_get_relativedelta_from_aggregation_interval_should_raise_key_error(timestring: str):
    with pytest.raises(KeyError):
        TimeUtils.get_relativedelta_from_aggregation_interval(timestring)
    with pytest.raises(KeyError):
        TimeUtils.get_timedelta_from_aggregation_interval(timestring)


@pytest.mark.parametrize(
    "timestring",
    ["", "0M", "test", "m"],
)
def test_get_relativedelta_from_aggregation_interval_should_raise_value_error(timestring: str):
    with pytest.raises(ValueError):
        TimeUtils.get_relativedelta_from_aggregation_interval(timestring)
    with pytest.raises(ValueError):
        TimeUtils.get_timedelta_from_aggregation_interval(timestring)


@pytest.mark.parametrize(
    ("timestring", "expected_output"),
    [
        ("1h", timedelta(hours=1)),
        ("10h", timedelta(hours=10)),
        ("1d", timedelta(days=1)),
        ("1D", timedelta(days=1)),
        ("10d", timedelta(days=10)),
        ("10D", timedelta(days=10)),
        ("1w", timedelta(weeks=1)),
        ("10w", timedelta(weeks=10)),
    ],
)
def test_get_timedelta_from_aggregation_interval(timestring: str, expected_output: relativedelta):
    assert TimeUtils.get_timedelta_from_aggregation_interval(timestring) == expected_output
