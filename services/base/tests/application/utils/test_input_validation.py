import datetime
from typing import List

import pytest

from services.base.application.input_validators.shared import InputTimeIntervalModel
from services.base.application.utils.input_validation import (
    are_sorted_entries_intervals_overlapping,
    is_none_except_fields,
)


@pytest.mark.parametrize(
    "values_dict,skip_set,expected_result",
    [
        ({"provider": "llif", "note": None, "diary_events": None, "ratings": None}, {"provider"}, True),
        ({"provider": None, "note": "test", "diary_events": None, "ratings": None}, {"provider"}, False),
        ({"note": "test", "diary_events": None, "ratings": None}, {"provider"}, False),
        ({"note": "test", "diary_events": None, "ratings": None}, None, False),
        (None, None, True),
        ([], {}, True),
    ],
)
def test_is_none_except_fields(values_dict, skip_set, expected_result):
    assert is_none_except_fields(values_dict, skip_set) == expected_result


@pytest.mark.parametrize(
    "entry_list, expected_output",
    [
        ([], False),
        (
            [
                InputTimeIntervalModel(
                    timestamp=datetime.datetime(2023, 6, 8, 10, 30, 0, tzinfo=datetime.timezone.utc),
                    end_time=datetime.datetime(2023, 6, 8, 10, 31, 0, tzinfo=datetime.timezone.utc),
                ),
            ],
            False,
        ),
        (
            [
                InputTimeIntervalModel(
                    timestamp=datetime.datetime(2023, 6, 8, 10, 30, 0, tzinfo=datetime.timezone.utc),
                    end_time=datetime.datetime(2023, 6, 8, 10, 31, 0, tzinfo=datetime.timezone.utc),
                ),
                InputTimeIntervalModel(
                    timestamp=datetime.datetime(2023, 6, 8, 10, 31, 0, tzinfo=datetime.timezone.utc),
                    end_time=datetime.datetime(2023, 6, 8, 10, 32, 0, tzinfo=datetime.timezone.utc),
                ),
            ],
            False,
        ),
        (
            [
                InputTimeIntervalModel(
                    timestamp=datetime.datetime(2023, 6, 8, 10, 30, 0, tzinfo=datetime.timezone.utc),
                    end_time=None,
                ),
                InputTimeIntervalModel(
                    timestamp=datetime.datetime(2023, 6, 8, 10, 31, 0, tzinfo=datetime.timezone.utc),
                    end_time=None,
                ),
            ],
            False,
        ),
        (
            [
                InputTimeIntervalModel(
                    timestamp=datetime.datetime(2023, 6, 8, 10, 31, 0, tzinfo=datetime.timezone.utc),
                    end_time=datetime.datetime(2023, 6, 8, 10, 32, 0, tzinfo=datetime.timezone.utc),
                ),
                InputTimeIntervalModel(
                    timestamp=datetime.datetime(2023, 6, 8, 10, 32, 0, tzinfo=datetime.timezone.utc),
                    end_time=datetime.datetime(2023, 6, 8, 10, 33, 0, tzinfo=datetime.timezone.utc),
                ),
                InputTimeIntervalModel(
                    timestamp=datetime.datetime(2023, 6, 8, 10, 31, 0, tzinfo=datetime.timezone.utc),
                    end_time=datetime.datetime(2023, 6, 8, 10, 33, 0, tzinfo=datetime.timezone.utc),
                ),
            ],
            True,
        ),
    ],
)
def test_are_entries_intervals_overlapping(entry_list: List[InputTimeIntervalModel], expected_output: bool):
    are_overlapping = are_sorted_entries_intervals_overlapping(entry_list=entry_list)
    assert are_overlapping == expected_output
