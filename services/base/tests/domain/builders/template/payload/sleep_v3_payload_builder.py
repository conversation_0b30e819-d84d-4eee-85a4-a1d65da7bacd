from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.enums.metadata_v3 import Insertable<PERSON>rigin, Origin
from services.base.domain.schemas.events.sleep_v3 import SleepV3Identifier
from services.base.domain.schemas.templates.payload.sleep_v3_template_payload import SleepV3TemplatePayload
from services.base.tests.domain.builders.sleep_builder_v3 import SleepV3<PERSON>uilder
from services.base.tests.domain.builders.template.payload.event_payload_base import EventPayloadBuilderBase


class SleepV3PayloadBuilder(EventPayloadBuilderBase, SleepV3Identifier):

    def build(self) -> SleepV3TemplatePayload:
        return SleepV3TemplatePayload.map(
            model=SleepV3Builder()
            .with_origin(
                origin=Origin(
                    (self._origin or PrimitiveTypesGenerator.generate_random_enum(enum_type=InsertableOrigin)).value
                )
            )
            .build()
        )
