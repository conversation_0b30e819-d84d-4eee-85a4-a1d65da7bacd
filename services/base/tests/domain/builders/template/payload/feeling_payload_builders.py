from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.enums.metadata_v3 import InsertableOrigin, Origin
from services.base.domain.schemas.events.feeling.emotion import EmotionIdentifier
from services.base.domain.schemas.events.feeling.stress import StressIdentifier
from services.base.domain.schemas.templates.payload.feeling_template_payloads import (
    EmotionTemplatePayload,
    StressTemplatePayload,
)
from services.base.tests.domain.builders.emotion_builder import EmotionBuilder
from services.base.tests.domain.builders.stress_builder import StressBuilder
from services.base.tests.domain.builders.template.payload.event_payload_base import EventPayloadBuilderBase


class EmotionPayloadBuilder(EventPayloadBuilderBase, EmotionIdentifier):

    def build(self) -> EmotionTemplatePayload:
        return EmotionTemplatePayload.map(
            model=EmotionBuilder()
            .with_origin(
                origin=Origin(
                    (self._origin or PrimitiveTypesGenerator.generate_random_enum(enum_type=InsertableOrigin)).value
                )
            )
            .build()
        )


class StressPayloadBuilder(EventPayloadBuilderBase, StressIdentifier):

    def build(self) -> StressTemplatePayload:
        return StressTemplatePayload.map(
            model=StressBuilder()
            .with_origin(
                origin=Origin(
                    (self._origin or PrimitiveTypesGenerator.generate_random_enum(enum_type=InsertableOrigin)).value
                )
            )
            .build()
        )
