import random

from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.enums.units.volume_unit import VolumeUnit
from services.base.domain.enums.units.weight_unit import WeightUnit
from services.base.domain.schemas.events.nutrition.drink import DrinkIdentifier
from services.base.domain.schemas.events.nutrition.food import FoodIdentifier
from services.base.domain.schemas.events.nutrition.nutrition_collection import NutritionValueLimits
from services.base.domain.schemas.events.nutrition.supplement import SupplementIdentifier
from services.base.domain.schemas.templates.payload.nutrition_collection_template_payloads import (
    DrinkTemplatePayload,
    FoodTemplatePayload,
    NutritionTemplateFields,
    SupplementTemplatePayload,
)
from services.base.tests.domain.builders.nutrition.drink_builder import DrinkBuilder
from services.base.tests.domain.builders.nutrition.food_builder import FoodBuilder
from services.base.tests.domain.builders.nutrition.supplement_builder import SupplementBuilder
from services.base.tests.domain.builders.template.payload.event_payload_base import EventPayloadBuilderBase


def _generate_random_additional_template_payload_fields(is_drink: bool = False) -> dict:
    amount = PrimitiveTypesGenerator.generate_random_float(
        min_value=NutritionValueLimits.MIN_AMOUNT, max_value=NutritionValueLimits.MAX_AMOUNT
    )
    if is_drink:
        amount_unit = PrimitiveTypesGenerator.generate_random_enum(enum_type=VolumeUnit)
    else:
        if random.choice([True, False]):
            amount_unit = PrimitiveTypesGenerator.generate_random_enum(enum_type=WeightUnit)
        else:
            amount_unit = PrimitiveTypesGenerator.generate_random_enum(enum_type=VolumeUnit)
    return {
        NutritionTemplateFields.ITEMS_PER_SERVING: PrimitiveTypesGenerator.generate_random_float(
            min_value=NutritionValueLimits.MIN_ITEMS_PER_SERVING,
            max_value=NutritionValueLimits.MAX_ITEMS_PER_SERVING,
        ),
        NutritionTemplateFields.AMOUNT: amount,
        NutritionTemplateFields.AMOUNT_UNIT: amount_unit,
    }


class DrinkPayloadBuilder(EventPayloadBuilderBase, DrinkIdentifier):

    def build(self) -> DrinkTemplatePayload:
        return DrinkTemplatePayload.map(
            model=DrinkBuilder()
            .with_name(self._name or PrimitiveTypesGenerator.generate_random_string())
            .with_tags(self._tags or PrimitiveTypesGenerator.generate_random_tags())
            .build(),
            fields=_generate_random_additional_template_payload_fields(is_drink=True),
        )


class FoodPayloadBuilder(EventPayloadBuilderBase, FoodIdentifier):

    def build(self) -> FoodTemplatePayload:
        return FoodTemplatePayload.map(
            FoodBuilder()
            .with_name(self._name or PrimitiveTypesGenerator.generate_random_string())
            .with_tags(self._tags or PrimitiveTypesGenerator.generate_random_tags())
            .build(),
            fields=_generate_random_additional_template_payload_fields(),
        )


class SupplementPayloadBuilder(EventPayloadBuilderBase, SupplementIdentifier):

    def build(self) -> SupplementTemplatePayload:
        return SupplementTemplatePayload.map(
            SupplementBuilder()
            .with_name(self._name or PrimitiveTypesGenerator.generate_random_string())
            .with_tags(self._tags or PrimitiveTypesGenerator.generate_random_tags())
            .build(),
            fields=_generate_random_additional_template_payload_fields(),
        )
