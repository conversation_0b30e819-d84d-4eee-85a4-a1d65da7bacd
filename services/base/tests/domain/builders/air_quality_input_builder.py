from datetime import datetime
from typing import Self, Sequence

from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.enums.data_types import DataType
from services.base.domain.repository.models.environment_inputs import (
    AirQualityInput,
)
from services.base.domain.schemas.air_quality import AirQualityAQI, AirQualityPollutants
from services.base.domain.schemas.environment import EnvironmentMetadata, EnvironmentSystemProperties
from services.base.domain.schemas.shared import CoordinatesModel


class AirQualityInputBuilder:
    def __init__(self):
        self._timestamp: datetime | None = None
        self._coordinates: CoordinatesModel | None = None

    def build(self) -> AirQualityInput:
        timestamp = self._timestamp or PrimitiveTypesGenerator.generate_random_aware_datetime()
        return AirQualityInput(
            type=DataType.AirQuality,
            timestamp=timestamp,
            coordinates=self._coordinates or PrimitiveTypesGenerator.get_random_coordinates(),
            metadata=EnvironmentMetadata(provider="OpenMeteo"),
            system_properties=EnvironmentSystemProperties(
                created_at=PrimitiveTypesGenerator.generate_random_aware_datetime()
            ),
            pollutants=AirQualityPollutants(
                pm10=PrimitiveTypesGenerator.generate_random_float(),
                pm25=PrimitiveTypesGenerator.generate_random_float(),
                co=PrimitiveTypesGenerator.generate_random_float(),
                o3=PrimitiveTypesGenerator.generate_random_float(),
                so2=PrimitiveTypesGenerator.generate_random_float(),
                no2=PrimitiveTypesGenerator.generate_random_float(),
            ),
            aqi=AirQualityAQI(eu=PrimitiveTypesGenerator.generate_random_int(), us=None, gb=None),
        )

    def with_timestamp(self, timestamp: datetime) -> Self:
        self._timestamp = timestamp
        return self

    def with_coordinates(self, coordinates: CoordinatesModel) -> Self:
        self._coordinates = coordinates
        return self

    def build_n(self, n: int | None = None) -> Sequence[AirQualityInput]:
        return [self.build() for _ in range(n or PrimitiveTypesGenerator.generate_random_int(min_value=1, max_value=5))]
