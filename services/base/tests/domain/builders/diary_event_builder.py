from __future__ import annotations

from datetime import datetime
from typing import Self, Sequence
from uuid import UUID, uuid4

from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.annotated_types import NonEmptyStr, Rounded6Float
from services.base.domain.enums.consumed_nutrition_type import ConsumedNutritionType
from services.base.domain.enums.diary_events.diary_events_type import DiaryEventType
from services.base.domain.enums.metadata import Organization
from services.base.domain.schemas.diary_events import (
    DiaryEvents,
    DiaryEventsConsumablesExtension,
    DiaryEventsPlanExtension,
)
from services.base.domain.value_limits.diary_events import (
    DiaryEventsConsumablesExtensionValueLimit,
    DiaryEventsValueLimit,
)
from services.base.tests.domain.builders.metadata_builder import MetadataBuilder


class DiaryEventBuilder:
    def __init__(self):
        self._metadata: MetadataBuilder = MetadataBuilder()
        self._consumable_extension: DiaryEventsConsumablesExtension | None = None
        self._timestamp: datetime | None = None
        self._name: str | None = None
        self._type: DiaryEventType | None = None
        self._is_standard: bool | None = None
        self._tags: list[str] | None = None
        self._plan_id: UUID | None = None

    def build_n(self, n: int | None = None) -> Sequence[DiaryEvents]:
        return [self.build() for _ in range(n or PrimitiveTypesGenerator.generate_random_int(min_value=1, max_value=5))]

    def build(self):
        event_type = self._type or PrimitiveTypesGenerator.generate_random_enum(enum_type=DiaryEventType)
        name = PrimitiveTypesGenerator.generate_random_string(max_length=DiaryEventsValueLimit.MAX_LENGTH)
        explanation = PrimitiveTypesGenerator.generate_random_string(max_length=50)
        metadata = self._metadata.build()
        timestamp = self._timestamp or PrimitiveTypesGenerator.generate_random_aware_datetime()
        end_time = PrimitiveTypesGenerator.generate_random_aware_datetime(gte=timestamp)

        return DiaryEvents(
            type=event_type,
            explanation=explanation,
            name=self._name or name,
            metadata=metadata,
            timestamp=timestamp,
            end_time=end_time,
            consumables_extension=self._consumable_extension,
            plan_extension=DiaryEventsPlanExtensionBuilder().with_plan_id(plan_id=self._plan_id or uuid4()).build(),
            is_standard=(
                self._is_standard if self._is_standard is not None else PrimitiveTypesGenerator.generate_random_bool()
            ),
            tags=self._tags
            or [
                PrimitiveTypesGenerator.generate_random_string(min_length=2, max_length=6)
                for _ in range(PrimitiveTypesGenerator.generate_random_int(min_value=1, max_value=3))
            ],
        )

    def with_user_uuid(self, user_uuid: UUID) -> DiaryEventBuilder:
        self._metadata = self._metadata.with_user_uuid(user_uuid)
        return self

    def with_organization(self, organization: Organization) -> DiaryEventBuilder:
        self._metadata = self._metadata.with_organization(organization)
        return self

    def with_name(self, name: NonEmptyStr) -> DiaryEventBuilder:
        self._name = name
        return self

    def with_tags(self, tags: list[str]) -> DiaryEventBuilder:
        self._tags = tags
        return self

    def with_type(self, event_type: DiaryEventType) -> DiaryEventBuilder:
        self._type = event_type
        return self

    def with_consumable_extension(self, ce: DiaryEventsConsumablesExtension | None) -> DiaryEventBuilder:
        self._consumable_extension = ce
        return self

    def with_timestamp(self, timestamp: datetime) -> DiaryEventBuilder:
        self._timestamp = timestamp
        return self

    def with_is_standard(self, is_standard: bool) -> DiaryEventBuilder:
        self._is_standard = is_standard
        return self

    def with_plan_id(self, plan_id: UUID) -> DiaryEventBuilder:
        self._plan_id = plan_id
        return self


class DiaryEventsConsumablesExtensionBuilder:

    def __init__(self):
        self._units: str | None = None
        self._quantity_type: ConsumedNutritionType | None = None
        self._amount: Rounded6Float | None = None

    def build(self) -> DiaryEventsConsumablesExtension:
        quantity_type = self._quantity_type or PrimitiveTypesGenerator.generate_random_enum(
            enum_type=ConsumedNutritionType
        )
        return DiaryEventsConsumablesExtension(
            name=PrimitiveTypesGenerator.generate_random_string(),
            quantity=PrimitiveTypesGenerator.generate_random_int(max_value=1000),
            quantity_type=quantity_type,
            units=self._units,
            amount=self._amount,
            calories=PrimitiveTypesGenerator.generate_random_int(
                min_value=DiaryEventsConsumablesExtensionValueLimit.MIN_CALORIES,
                max_value=DiaryEventsConsumablesExtensionValueLimit.MAX_CALORIES,
            ),
            fat=PrimitiveTypesGenerator.generate_random_int(
                min_value=DiaryEventsConsumablesExtensionValueLimit.MIN_FAT,
                max_value=DiaryEventsConsumablesExtensionValueLimit.MAX_FAT,
            ),
            carbohydrates=PrimitiveTypesGenerator.generate_random_int(
                min_value=DiaryEventsConsumablesExtensionValueLimit.MIN_CARBOHYDRATES,
                max_value=DiaryEventsConsumablesExtensionValueLimit.MAX_CARBOHYDRATES,
            ),
            protein=PrimitiveTypesGenerator.generate_random_int(
                min_value=DiaryEventsConsumablesExtensionValueLimit.MIN_PROTEIN,
                max_value=DiaryEventsConsumablesExtensionValueLimit.MAX_PROTEIN,
            ),
            sodium=PrimitiveTypesGenerator.generate_random_int(
                min_value=DiaryEventsConsumablesExtensionValueLimit.MIN_SODIUM,
                max_value=DiaryEventsConsumablesExtensionValueLimit.MAX_SODIUM,
            ),
        )

    def with_units(self, units: str | None) -> Self:
        self._units = units
        return self

    def with_quantity_type(self, quantity_type: ConsumedNutritionType) -> Self:
        self._quantity_type = quantity_type
        return self

    def with_amount(self, amount: Rounded6Float | None) -> Self:
        self._amount = amount
        return self


class DiaryEventsPlanExtensionBuilder:
    _plan_id: UUID | None = None

    def build(self) -> DiaryEventsPlanExtension:
        return DiaryEventsPlanExtension(
            plan_id=self._plan_id or uuid4(), scheduled_at=PrimitiveTypesGenerator.generate_random_aware_datetime()
        )

    def with_plan_id(self, plan_id: UUID) -> DiaryEventsPlanExtensionBuilder:
        self._plan_id = plan_id
        return self
