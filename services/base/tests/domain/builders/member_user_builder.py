from __future__ import annotations

from datetime import datetime, timezone
from uuid import UUID, uuid4

from services.base.domain.enums.member_user_role import MemberUserType
from services.base.domain.schemas.member_user.member_user import MemberUser


class MemberUserBuilder:
    def __init__(self):
        self._uuid: UUID | None = None
        self._type: MemberUserType | None = None
        self._last_logged_at: datetime | None = None
        self._created_at: datetime | None = None
        self._primary_email: str | None = None

    def with_last_logged_at(self, timestamp: datetime) -> MemberUserBuilder:
        self._last_logged_at = timestamp
        return self

    def with_created_at(self, timestamp: datetime) -> MemberUserBuilder:
        self._created_at = timestamp
        return self

    def with_uuid(self, uuid: UUID) -> MemberUserBuilder:
        self._uuid = uuid
        return self

    def with_type(self, user_type: MemberUserType) -> MemberUserBuilder:
        self._type = user_type
        return self

    def with_primary_email(self, email: str | None) -> MemberUserBuilder:
        self._primary_email = email
        return self

    def build(self) -> MemberUser:
        uuid = self._uuid or uuid4()
        now = datetime.now(timezone.utc)
        created_at = self._created_at or now
        last_logged_at = self._last_logged_at or now

        return MemberUser(
            user_uuid=uuid,
            primary_email=self._primary_email,
            last_logged_at=last_logged_at,
            created_at=created_at,
            type=self._type or MemberUserType.STANDARD,
        )
