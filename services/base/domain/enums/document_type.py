from enum import StrEnum

from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.document_base import Document
from services.base.type_resolver import TypeResolver

"""
Contains only v3 models
"""


class DocumentType(StrEnum):
    # Body Metric Collection
    BloodGlucose = DataType.BloodGlucose
    BloodPressure = DataType.BloodPressure
    BodyMetric = DataType.BodyMetric

    # Content Collection
    Audio = DataType.Audio
    Content = DataType.Content
    Image = DataType.Image
    Interactive = DataType.Interactive
    Text = DataType.Text
    Video = DataType.Video

    # Exercise Collection
    Cardio = DataType.Cardio
    Exercise = DataType.Exercise
    Strength = DataType.Strength

    # Feeling Collection
    Emotion = DataType.Emotion
    Stress = DataType.Stress

    # Nutrition Collection
    Drink = DataType.Drink
    Food = DataType.Food
    Supplement = DataType.Supplement

    # Other Events
    Activity = DataType.Activity
    SleepV3 = DataType.SleepV3
    Note = DataType.Note
    Symptom = DataType.Symptom
    Medication = DataType.Medication
    Person = DataType.Person

    # Documents
    EventTemplate = DataType.EventTemplate
    GroupTemplate = DataType.GroupTemplate
    Plan = DataType.Plan
    UseCase = DataType.UseCase
    EventGroup = DataType.EventGroup

    def to_domain_model(self) -> type[Document]:
        return TypeResolver.get_document(self)
