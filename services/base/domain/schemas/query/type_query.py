from typing import Sequence

from pydantic import Field, model_validator

from services.base.domain.schemas.events.document_base import Document
from services.base.domain.schemas.events.event import Event
from services.base.domain.schemas.query.boolean_query import BooleanQuery
from services.base.domain.schemas.query.leaf_query import LeafQuery
from services.base.domain.schemas.query.validators.boolean_query_validator import (
    BooleanQueryValidator,
)
from services.base.domain.schemas.query.validators.leaf_query_validator import LeafQueryValidator
from services.base.domain.schemas.shared import BaseDataModel
from services.base.domain.schemas.templates.event_template import EventTemplate
from services.base.domain.schemas.templates.group_template import GroupTemplate
from services.base.domain.schemas.templates.template import Template
from services.base.type_resolver import TypeResolver


class TypeQuery(BaseDataModel):
    domain_types: Sequence[type[Document]] = Field(min_length=1)
    query: LeafQuery | BooleanQuery | None = Field(union_mode="left_to_right")

    @model_validator(mode="after")
    def _query_validation(self):
        if self.query is None:
            # Nothing to validate, filtering only based on the document type
            return self
        elif isinstance(self.query, LeafQuery):
            LeafQueryValidator.validate(leaf_query=self.query, domain_types=self.domain_types)
            return self
        elif isinstance(self.query, BooleanQuery):
            BooleanQueryValidator.validate(boolean_query=self.query, domain_types=self.domain_types)
            return self
        else:
            raise TypeError(f"Expected LeafQuery or BooleanQuery, but got {type(self.query)}]")

    @model_validator(mode="after")
    def _convert_abstract_types(self):
        if Event in self.domain_types:
            self.domain_types = list({*self.domain_types, *TypeResolver.EVENTS_V3} - {Event})
        if Template in self.domain_types:
            self.domain_types = list({*self.domain_types, EventTemplate, GroupTemplate} - {Template})
        return self
