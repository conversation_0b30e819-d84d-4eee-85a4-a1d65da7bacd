from dataclasses import dataclass
from datetime import datetime, timezone

from pydantic import Field, model_validator

from services.base.domain.annotated_types import SerializableAwareDatetime
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.schemas.events.document_base import Document
from services.base.domain.schemas.shared import BaseDataModel, CoordinatesModel, TimestampModel


@dataclass(frozen=True)
class EnvironmentMetadataLabels:
    PROVIDER: str = "provider"
    DATA_INTEGRITY: str = "data_integrity"
    DATA_QUALITY: str = "data_quality"


@dataclass(frozen=True)
class EnvironmentLabels:
    COORDINATES: str = "coordinates"
    METADATA: str = DocumentLabels.METADATA
    SYSTEM_PROPERTIES: str = DocumentLabels.SYSTEM_PROPERTIES


class EnvironmentSystemProperties(BaseDataModel):
    created_at: SerializableAwareDatetime = Field(
        alias=DocumentLabels.CREATED_AT, default_factory=lambda: datetime.now(timezone.utc)
    )
    backfill: bool = Field(alias=DocumentLabels.BACKFILL, default=False)


class SpaceTimeInput(TimestampModel, CoordinatesModel):
    end_time: SerializableAwareDatetime = Field(alias=DocumentLabels.END_TIME)

    @model_validator(mode="after")
    def validate_timestamp(self):
        if self.timestamp > self.end_time:
            raise ValueError("timestamp has to be less than end_time")

        return self


class EnvironmentMetadata(BaseDataModel):
    provider: str = Field(alias=EnvironmentMetadataLabels.PROVIDER)


class Environment(TimestampModel, Document):
    id: str = Field(alias=DocumentLabels.ID, description="uuid extended with date suffix")  # pyright: ignore
    coordinates: CoordinatesModel = Field(alias=EnvironmentLabels.COORDINATES)
    metadata: EnvironmentMetadata = Field(alias=EnvironmentLabels.METADATA)
    system_properties: EnvironmentSystemProperties = Field(alias=EnvironmentLabels.SYSTEM_PROPERTIES)
