from dataclasses import dataclass
from enum import StrEnum
from typing import Literal

from pydantic import Field

from services.base.domain.annotated_types import RoundedFloat
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.event import Event, EventFields, EventValueLimits
from services.base.domain.schemas.events.type_identifier import TypeIdentifier


@dataclass(frozen=True)
class CardioFields(EventFields):
    DISTANCE = "distance"
    ELEVATION = "elevation"
    RATING = "rating"


class CardioCategory(StrEnum):
    RUN = "run"
    WALK = "walk"
    HIKE = "hike"
    DANCE = "dance"
    JUMP_ROPE = "jump_rope"
    ROW = "row"
    ELLIPTICAL = "elliptical"
    STAIR_CLIMB = "stair_climb"
    SWIM = "swim"
    CYCLING = "cycling"
    CARDIO = "cardio"


class CardioIdentifier(TypeIdentifier):

    @classmethod
    def type_id(cls) -> str:
        return DataType.Cardio


class Cardio(Event, CardioIdentifier):
    type: Literal[DataType.Cardio] = Field(alias=CardioFields.TYPE)
    category: CardioCategory = Field(alias=EventFields.CATEGORY)
    distance: RoundedFloat | None = Field(alias=CardioFields.DISTANCE, ge=0, le=250_000)
    elevation: RoundedFloat | None = Field(alias=CardioFields.ELEVATION, ge=-500, le=8848)
    rating: int | None = Field(
        alias=CardioFields.RATING,
        ge=EventValueLimits.RATING_MINIMUM_VALUE,
        le=EventValueLimits.RATING_MAXIMUM_VALUE,
    )
