from enum import StrEnum, auto
from typing import Literal

from pydantic import Field

from services.base.domain.annotated_types import NonEmptyStr, Rounded6Float
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.medication.medication import (
    ConsumeUnit,
    MedicationCategory,
    MedicationFields,
    MedicationIdentifier,
    MedicationValueLimits,
    SingleDoseInformation,
    VolumeUnit,
    WeightUnit,
)
from services.base.domain.schemas.shared import BaseDataModel
from services.base.domain.schemas.templates.payload.template_payload_base import TemplatePayloadBase


class Administration(StrEnum):
    CAPSULE = auto()
    TABLET = auto()
    ORAL = auto()
    INJECTION = auto()
    PATCH = auto()
    SUPPOSITORY = auto()
    TOPICAL = auto()
    DROPS = auto()
    INHALER = auto()
    SPRAY = auto()
    OTHER = auto()


class MedicationDetails(BaseDataModel):
    brand: NonEmptyStr | None = Field(
        alias=MedicationFields.BRAND, max_length=MedicationValueLimits.MAX_BRAND_NAME_LENGTH
    )
    generic_name: NonEmptyStr | None = Field(
        alias=MedicationFields.GENERIC_NAME, max_length=MedicationValueLimits.MAX_BRAND_NAME_LENGTH
    )
    rx_cuid: NonEmptyStr | None = Field(alias=MedicationFields.RX_CUID, description="External identifier from RxNorm")
    administration: Administration = Field(alias=MedicationFields.ADMINISTRATION)


class MedicationTemplatePayload(TemplatePayloadBase, MedicationIdentifier):
    type: Literal[DataType.Medication] = Field(alias=MedicationFields.TYPE)
    category: MedicationCategory = Field(alias=MedicationFields.CATEGORY)

    medication_details: MedicationDetails = Field(alias=MedicationFields.MEDICATION_DETAILS)
    single_dose_information: SingleDoseInformation = Field(alias=MedicationFields.SINGLE_DOSE_INFORMATION)
    consumed_amount: Rounded6Float = Field(
        alias=MedicationFields.CONSUMED_AMOUNT,
        ge=MedicationValueLimits.MIN_CONSUMED_QUANTITY,
        le=MedicationValueLimits.MAX_CONSUMED_QUANTITY,
    )
    consume_unit: VolumeUnit | WeightUnit | ConsumeUnit = Field(alias=MedicationFields.CONSUME_UNIT)
