from abc import ABC, abstractmethod
from typing import Optional, Sequence
from uuid import UUID

from services.base.application.database.models.sorts import Sort
from services.base.domain.repository.models.search_results import SearchResults
from services.base.domain.schemas.contact import Contact
from services.base.domain.schemas.query.query import Query


class ContactRepository(ABC):
    @abstractmethod
    async def insert(self, contacts: Sequence[Contact], force_strong_consistency: bool = False) -> Sequence[Contact]:
        pass

    @abstractmethod
    async def update(self, contacts: Sequence[Contact]) -> Sequence[Contact]:
        pass

    @abstractmethod
    async def search_by_id(self, ids: Sequence[UUID]) -> Sequence[Contact]:
        pass

    @abstractmethod
    async def search_by_query(
        self,
        query: Query,
        size: int,
        sorts: Optional[Sequence[Sort]] = None,
        continuation_token: Optional[str] = None,
    ) -> SearchResults[Contact]:
        pass

    @abstractmethod
    async def delete_by_id(self, ids: Sequence[UUID]) -> Sequence[UUID]:
        pass
