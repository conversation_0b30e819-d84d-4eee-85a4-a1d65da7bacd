from abc import ABC, abstractmethod
from typing import Optional, Sequence
from uuid import UUID

from services.base.domain.repository.wrappers import ReadFromDatabaseWrapper
from services.base.domain.schemas.extensions.extension_provider import ExtensionProvider


class ExtensionProviderRepository(ABC):
    @abstractmethod
    async def get(self, wrapper: ReadFromDatabaseWrapper) -> Sequence[ExtensionProvider]:
        pass

    @abstractmethod
    async def delete(self, extension_providers: Sequence[ExtensionProvider]) -> None:
        pass

    @abstractmethod
    async def get_by_provider_id(self, provider_id: UUID) -> Optional[ExtensionProvider]:
        pass

    @abstractmethod
    async def upsert(self, extension_providers: Sequence[ExtensionProvider]) -> Sequence[ExtensionProvider]:
        pass
