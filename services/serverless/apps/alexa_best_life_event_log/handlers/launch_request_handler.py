from typing import Union

import ask_sdk_core.utils as ask_utils
from ask_sdk_core.dispatch_components import AbstractRe<PERSON><PERSON><PERSON><PERSON>
from ask_sdk_core.handler_input import HandlerInput
from ask_sdk_model import Response

from services.serverless.apps.alexa_best_life_event_log.domain.phrases import EXAMPLE_COMMAND_USAGE
from services.serverless.apps.alexa_best_life_event_log.domain.request_types import RequestType


class LaunchRequestHandler(AbstractRequestHandler):
    """Handler for launching the Best Life skill action"""

    def can_handle(self, handler_input: HandlerInput) -> bool:
        return ask_utils.is_request_type(RequestType.LAUNCH_REQUEST.value)(handler_input)

    def handle(self, handler_input: HandlerInput) -> Union[None, Response]:
        speak_output = f"Welcome to Best Life! You can say, {EXAMPLE_COMMAND_USAGE}. What do you want to log?"

        handler_input.response_builder.speak(speak_output).set_should_end_session(False)

        return handler_input.response_builder.response
