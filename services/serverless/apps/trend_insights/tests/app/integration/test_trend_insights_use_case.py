import json
from datetime import datetime, timezone
from typing import List
from uuid import uuid4

from services.base.application.event_models.analytics_completed_event_model import AnalyticsCompletedEventModel
from services.base.domain.constants.messaging import ATT_NAME_ANALYTICS_EVENT, MessageTopics
from services.base.domain.enums.analytics.extension_output import ExtensionStatus
from services.base.domain.enums.analytics.trend_insights import TrendDirection, TrendInsightsResultStatus
from services.base.domain.repository.extension_result_repository import ExtensionResultRepository
from services.base.domain.repository.extension_run_repository import ExtensionRunRepository
from services.base.domain.schemas.events.feeling.stress import StressCategory
from services.base.domain.schemas.member_user.member_user import MemberUser
from services.base.message_queue.utils import create_string_message_attribute
from services.serverless.apps.trend_insights.app.boundaries.trend_insights_input_boundary import (
    TrendInsightsInputBoundary,
)
from services.serverless.apps.trend_insights.app.constants.messages import TrendInsightsResultMessages
from services.serverless.apps.trend_insights.app.models.analytic_series_models import SeriesAnalysisOutputModel
from services.serverless.apps.trend_insights.app.models.analytic_type import AnalyticType
from services.serverless.apps.trend_insights.app.models.trend_insights_models import (
    TrendInsightsInput,
    TrendInsightsSeriesOutput,
)
from services.serverless.apps.trend_insights.app.trend_insights_use_case import TrendInsightsUseCase
from services.serverless.apps.trend_insights.dependency_bootstrapper import DependencyBootstrapper
from settings.extension_constants import LLIF_EXTENSION_PROVIDER_UUID, TREND_INSIGHTS_EXTENSION_ID


async def test_trend_insights_use_case_user_with_no_data_passes(
    dependency_bootstrapper: DependencyBootstrapper, user_with_no_data: MemberUser
):
    # arrange
    user_uuid = user_with_no_data.user_uuid
    analytic_types: List[AnalyticType] = [analytic_type for analytic_type in AnalyticType]
    # act
    stored_run, stored_results = await dependency_bootstrapper.get(TrendInsightsUseCase).execute_async(
        input_boundary=TrendInsightsInputBoundary(
            extension_id=TREND_INSIGHTS_EXTENSION_ID,
            extension_input=TrendInsightsInput(analytic_types=analytic_types, should_notify=True),
            provider_id=LLIF_EXTENSION_PROVIDER_UUID,
            user_uuid=user_uuid,
        )
    )
    # asserts
    assert stored_run
    assert stored_run.extension_status == ExtensionStatus.SUCCEEDED
    assert not stored_run.result_status
    assert not stored_results

    # cleanup
    await dependency_bootstrapper.get(ExtensionRunRepository).delete_by_id(ids=[stored_run.id])


async def test_trend_insights_use_case_user_with_multiple_short_term_data_passes(
    dependency_bootstrapper: DependencyBootstrapper, user_with_multiple_data: MemberUser
):
    # arrange
    user_uuid = user_with_multiple_data.user_uuid
    analytic_types: List[AnalyticType] = [analytic_type for analytic_type in AnalyticType]
    # act
    stored_run, stored_results = await dependency_bootstrapper.get(TrendInsightsUseCase).execute_async(
        input_boundary=TrendInsightsInputBoundary(
            extension_id=TREND_INSIGHTS_EXTENSION_ID,
            extension_input=TrendInsightsInput(analytic_types=analytic_types, should_notify=True),
            provider_id=LLIF_EXTENSION_PROVIDER_UUID,
            user_uuid=user_uuid,
        )
    )
    # asserts
    assert stored_run
    assert stored_run.extension_status == ExtensionStatus.SUCCEEDED
    assert stored_results
    for stored_result in stored_results:
        series_results = TrendInsightsSeriesOutput(**json.loads(stored_result.output))
        for individual_result in series_results.series_analysis_results:
            individual_result: SeriesAnalysisOutputModel
            assert individual_result.result_status in [
                TrendInsightsResultStatus.CALCULATED_STATISTICS_AND_TREND,
                TrendInsightsResultStatus.CALCULATED_STATISTICS,
            ], f"Unexpected status: {individual_result.result_status} returned for trend insights of {series_results.analytic_series}"

    # cleanup
    await dependency_bootstrapper.get(ExtensionRunRepository).delete_by_id(ids=[stored_run.id])
    await dependency_bootstrapper.get(ExtensionResultRepository).delete_by_id(
        ids=[result.id for result in stored_results]
    )


async def test_publish_trend_insights_result_is_called_with_correct_arguments(
    publish_test_trend_insights_use_case: TrendInsightsUseCase,
):
    user_id = uuid4()
    doc_id = uuid4()
    extension_id = TREND_INSIGHTS_EXTENSION_ID
    result_status = True
    time_of_completion = datetime.now(timezone.utc)

    await publish_test_trend_insights_use_case.publish_trend_insights_result(
        user_id=user_id,
        doc_id=doc_id,
        extension_id=extension_id,
        result_status=result_status,
        time_of_completion=time_of_completion,
    )

    publish_test_trend_insights_use_case._message_broker_client.publish_topic.assert_called_with(  # pyright: ignore
        topic_name=MessageTopics.TOPIC_ANALYTIC_FINISHED.value,
        message_body=AnalyticsCompletedEventModel(
            doc_id=doc_id,
            user_uuid=user_id,
            timestamp=time_of_completion,
            extension_id=extension_id,
            title=(
                TrendInsightsResultMessages.RUN_WITH_OUTPUT_TITLE
                if result_status
                else TrendInsightsResultMessages.RUN_WITHOUT_OUTPUT_TITLE
            ),
        ).model_dump_json(),
        message_attributes=create_string_message_attribute(
            ATT_NAME_ANALYTICS_EVENT, MessageTopics.TOPIC_ANALYTIC_FINISHED.value
        ),
    )


async def test_evaluate_analytics_user_with_one_datatype_yearly_data_passes(
    dependency_bootstrapper: DependencyBootstrapper,
    user_with_yearly_emotion_data: MemberUser,
):
    # arrange
    user = user_with_yearly_emotion_data
    # act
    extension_output = await dependency_bootstrapper.get(TrendInsightsUseCase).evaluate_analytics(
        TrendInsightsInputBoundary(
            extension_id=TREND_INSIGHTS_EXTENSION_ID,
            extension_input=TrendInsightsInput(analytic_types=[AnalyticType.Emotion], should_notify=True),
            provider_id=LLIF_EXTENSION_PROVIDER_UUID,
            user_uuid=user.user_uuid,
        )
    )

    # assert
    assert extension_output
    assert extension_output.trend_insights_result_documents
    for result in extension_output.trend_insights_result_documents:
        series_results = TrendInsightsSeriesOutput(**json.loads(result.output))
        for individual_result in series_results.series_analysis_results:
            individual_result: SeriesAnalysisOutputModel
            assert individual_result.result_status in [
                TrendInsightsResultStatus.CALCULATED_STATISTICS_AND_TREND,
                TrendInsightsResultStatus.CALCULATED_STATISTICS,
            ], f"Unexpected status: {individual_result.result_status} returned for trend insights of {series_results.analytic_series}"
    # validate the extension output is serializable
    assert extension_output.model_dump_json()


async def test_evaluate_analytics_user_with_one_datatype_short_term_specific_trends_should_pass(
    dependency_bootstrapper: DependencyBootstrapper,
    user_with_short_term_trending_stress_data: MemberUser,
):
    user = user_with_short_term_trending_stress_data
    expected_series_to_trend = {
        StressCategory.PHYSICAL_ACTIVITY: TrendDirection.DECREASING,
        StressCategory.MENTAL_ACTIVITY: TrendDirection.INCREASING,
        StressCategory.SOCIAL_ACTIVITY: TrendDirection.NON_MONOTONOUS,
    }
    # arrange
    # act
    extension_output = await dependency_bootstrapper.get(TrendInsightsUseCase).evaluate_analytics(
        TrendInsightsInputBoundary(
            extension_id=TREND_INSIGHTS_EXTENSION_ID,
            extension_input=TrendInsightsInput(analytic_types=[AnalyticType.Stress], should_notify=True),
            provider_id=LLIF_EXTENSION_PROVIDER_UUID,
            user_uuid=user.user_uuid,
        )
    )
    # assert
    assert extension_output
    assert extension_output.trend_insights_result_documents
    for result in extension_output.trend_insights_result_documents:
        series_results = TrendInsightsSeriesOutput(**json.loads(result.output))

        short_term_result = series_results.series_analysis_results[0]
        assert short_term_result.evaluation_output
        evaluation_output = short_term_result.evaluation_output
        assert evaluation_output.trend
        trend = evaluation_output.trend.consecutive_trend
        expected_trend = expected_series_to_trend[StressCategory(series_results.analytic_series)]
        assert trend == expected_trend

        for individual_result in series_results.series_analysis_results:
            individual_result: SeriesAnalysisOutputModel
            assert individual_result.result_status in [
                TrendInsightsResultStatus.CALCULATED_STATISTICS_AND_TREND,
                TrendInsightsResultStatus.CALCULATED_STATISTICS,
            ], f"Unexpected status: {individual_result.result_status} returned for trend insights of {series_results.analytic_series}"
    # validate the extension output is serializable
    assert extension_output.model_dump_json()
