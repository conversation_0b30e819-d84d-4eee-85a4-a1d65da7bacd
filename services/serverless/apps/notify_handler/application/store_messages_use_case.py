import asyncio
import json
from datetime import datetime, timezone
from typing import Sequence
from uuid import uuid4
from zoneinfo import ZoneInfo

from services.base.domain.enums.context_type import ContextType
from services.base.domain.enums.messages import InboxMessageStatus
from services.base.domain.enums.notification import NotificationStatus
from services.base.domain.repository.inbox_message_repository import InboxMessageRepository
from services.base.domain.repository.member_user_settings_repository import MemberUserSettingsRepository
from services.base.domain.repository.notification_inbox_repository import NotificationInboxRepository
from services.base.domain.schemas.inbox.inbox_message import InboxMessage
from services.base.domain.schemas.inbox.member_user_notification_inbox import MemberUserNotificationInbox
from services.serverless.apps.notify_handler.domain.notification_input import NotificationInput


class StoreMessagesUseCase:
    def __init__(
        self,
        member_user_settings_repository: MemberUserSettingsRepository,
        notification_repository: NotificationInboxRepository,
        inbox_message_repository: InboxMessageRepository,
    ):

        self._member_user_settings_repository = member_user_settings_repository
        self._notification_repository = notification_repository
        self._inbox_message_repository = inbox_message_repository

    async def execute_async(self, notification_inputs: Sequence[NotificationInput]) -> Sequence[InboxMessage]:
        tasks: list[asyncio.Task] = []

        for notification in notification_inputs:
            if notification.is_important:
                tasks.append(asyncio.create_task(self.store_notification_inputs(notification_input=notification)))

        return await asyncio.gather(*tasks)

    async def store_notification_inputs(self, notification_input: NotificationInput) -> InboxMessage:
        user_settings = await self._member_user_settings_repository.get_by_uuid(
            user_uuid=notification_input.destination.id
        )
        user_timezone = user_settings.general.timezone if user_settings else ZoneInfo(str(timezone.utc))

        notification = await self.create_notification(
            notification_input=notification_input, user_timezone=user_timezone
        )
        await self._notification_repository.upsert(notifications=[notification])
        message = await self.create_inbox_message(notification_input=notification_input, user_timezone=user_timezone)
        messages = await self._inbox_message_repository.insert(messages=[message])
        return messages[0]

    @staticmethod
    async def create_notification(
        notification_input: NotificationInput,
        user_timezone: ZoneInfo,
    ) -> MemberUserNotificationInbox:
        """Creates a notification object if the analytics output values meet importance and urgency criteria"""
        # remap actions, TODO(jaja): delete together with deprecated notifications
        context = None
        if notification_input.context:
            context = notification_input.context.model_dump(by_alias=True)
            match notification_input.context.type:
                case ContextType.EXPORT_FINISHED:
                    context |= {"type": ContextType.EXPORT}
                case ContextType.EXTENSION_FINISHED:
                    context |= {"type": ContextType.EXTENSION}
                case _:
                    ...
        notification = MemberUserNotificationInbox(
            user_uuid=notification_input.destination.id,
            timestamp=datetime.now(tz=user_timezone),
            type=notification_input.category,
            urgent=notification_input.is_urgent,
            important=notification_input.is_important,
            title=notification_input.title,
            description=notification_input.description,
            message=notification_input.message,
            action=json.dumps(context, default=str) if context else None,
            status=NotificationStatus.UNREAD,
        )
        return notification

    @staticmethod
    async def create_inbox_message(notification_input: NotificationInput, user_timezone: ZoneInfo) -> InboxMessage:
        return InboxMessage(
            destination=notification_input.destination,
            sender=notification_input.sender,
            timestamp=datetime.now(tz=user_timezone),
            title=notification_input.title,
            message=notification_input.message,
            context=notification_input.context,
            status=InboxMessageStatus.UNREAD,
            is_urgent=notification_input.is_urgent,
            id=uuid4(),
        )
