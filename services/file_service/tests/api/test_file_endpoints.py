import asyncio

import pytest
from httpx import ASGITransport, AsyncClient
from starlette import status

from services.base.api.authentication.token_handling import generate_access_token
from services.base.application.exceptions import NoContentException
from services.base.domain.enums.event_type import EventType
from services.base.domain.enums.metadata import Organization
from services.data_service.application.use_cases.data_summary_use_case import DataSummaryUseCase
from services.file_service.api.urls import FileEndpointUrls
from services.file_service.application.loaders.run_loaders import run_loaders
from services.file_service.main import app


# TODO(jaja): test is conceptually wrong as user data UC belongs to a different service.
# Also, it is missing the file deletion part - right now, we have no way to check whether user has any files uploaded.
async def test_delete_all_user_content_should_be_deleted_and_not_found(
    depr_event_repository,
    user_factory,
    search_service,
):
    # Arrange
    user = await user_factory()
    headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}
    client = AsyncClient(transport=ASGITransport(app=app), base_url="http://test")
    run_loaders(user_uuid=user.user_uuid)
    request_url = FileEndpointUrls.DELETE_ALL_DATA

    await asyncio.sleep(1)
    # Ensures there are data for the user, otherwise it would throw an error.
    await DataSummaryUseCase(search_service=search_service).execute(
        user_uuid=user.user_uuid,
        data_type=[data_type for data_type in EventType],
        organizations=[organization for organization in Organization],
    )

    # Act
    delete_response = await client.delete(request_url, headers=headers)

    # Assert
    assert delete_response.status_code == status.HTTP_202_ACCEPTED

    await asyncio.sleep(3)
    with pytest.raises(NoContentException):
        await DataSummaryUseCase(search_service=search_service).execute(
            user_uuid=user.user_uuid,
            data_type=[data_type for data_type in EventType],
            organizations=[organization for organization in Organization],
        )
