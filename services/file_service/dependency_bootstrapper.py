from typing import Self, Type

from aioboto3 import Session
from azure.storage.blob.aio import BlobServiceClient
from injector import Injector
from opensearchpy import Async<PERSON>penSearch
from sqlalchemy import URL
from sqlalchemy.ext.asyncio import Async<PERSON><PERSON><PERSON>, Async<PERSON>ession, async_sessionmaker, create_async_engine

from services.base.application.async_message_broker_client import AsyncMessageBrokerClient
from services.base.application.database.depr_event_repository import DeprEventRepository
from services.base.application.database.depr_job_service import DeprJobService
from services.base.application.database.document_search_service import DocumentSearchService
from services.base.application.message_broker_client import MessageBrokerClient
from services.base.application.message_queue_client import MessageQueueClient
from services.base.application.object_storage_service import ObjectStorageService
from services.base.domain.repository.environment_repository import EnvironmentRepository
from services.base.domain.repository.event_repository import EventRepository
from services.base.domain.repository.export_task_repository import ExportTaskRepository
from services.base.domain.repository.extension_detail_repository import ExtensionDetailRepository
from services.base.domain.repository.extension_result_repository import ExtensionResultRepository
from services.base.domain.repository.extension_run_repository import ExtensionRunRepository
from services.base.domain.repository.member_user_os_tasks_repository import MemberUserOSTasksRepository
from services.base.domain.repository.member_user_repository import MemberUserRepository
from services.base.domain.repository.member_user_settings_repository import MemberUserSettingsRepository
from services.base.domain.repository.plan_repository import PlanRepository
from services.base.domain.repository.template_repository import TemplateRepository
from services.base.domain.repository.use_case_repository import UseCaseRepository
from services.base.infrastructure.aws.async_sns_wrapper import AsyncSNSWrapper
from services.base.infrastructure.aws.service_provider import AWSServiceProvider
from services.base.infrastructure.aws.sns_wrapper import SNSWrapper
from services.base.infrastructure.aws.sqs_wrapper import SQSWrapper
from services.base.infrastructure.azure.blob_storage_service import BlobStorageService
from services.base.infrastructure.database.opensearch.depr_opensearch_job_service import DeprOpenSearchJobService
from services.base.infrastructure.database.opensearch.opensearch_client import OpenSearchClient
from services.base.infrastructure.database.opensearch.os_depr_event_repository import OSDeprEventRepository
from services.base.infrastructure.database.opensearch.os_document_search_service import OSDocumentSearchService
from services.base.infrastructure.database.opensearch.repository.os_environment_repository import (
    OSEnvironmentRepository,
)
from services.base.infrastructure.database.opensearch.repository.os_event_repository import OSEventRepository
from services.base.infrastructure.database.opensearch.repository.os_extension_result_repository import (
    OSExtensionResultRepository,
)
from services.base.infrastructure.database.opensearch.repository.os_extension_run_repository import (
    OSExtensionRunRepository,
)
from services.base.infrastructure.database.opensearch.repository.os_plan_repository import OSPlanRepository
from services.base.infrastructure.database.opensearch.repository.os_template_repository import OSTemplateRepository
from services.base.infrastructure.database.opensearch.repository.os_use_case_repository import OSUseCaseRepository
from services.base.infrastructure.database.sql_alchemy.repository.sql_alch_export_task_repository import (
    SqlAlchExportTaskRepository,
)
from services.base.infrastructure.database.sql_alchemy.repository.sql_alch_extension_detail_repository import (
    SqlAlchExtensionDetailRepository,
)
from services.base.infrastructure.database.sql_alchemy.repository.sql_alch_member_user_os_tasks_repository import (
    SqlAlchMemberUserOSTasksRepository,
)
from services.base.infrastructure.database.sql_alchemy.repository.sql_alch_member_user_repository import (
    SqlAlchMemberUserRepository,
)
from services.base.infrastructure.database.sql_alchemy.repository.sql_alch_member_user_settings_repository import (
    SqlAlchMemberUserSettingsRepository,
)
from services.base.tasks.worker_orchestrator import ProcessPoolOrchestrator, WorkerOrchestrator
from services.data_service.application.use_cases.delete_by_filter_use_case import DeleteByFilterUseCase
from services.file_service.application.use_cases.cancel_file_upload_use_case import CancelFileUploadUseCase
from services.file_service.application.use_cases.delete_all_user_data_use_case import DeleteAllUserDataUseCase
from services.file_service.application.use_cases.export.fetch_export_url_use_case import FetchExportUrlUseCase
from services.file_service.application.use_cases.export.process_data_export_use_case import ProcessDataExportUseCase
from services.file_service.application.use_cases.export.schedule_data_export_use_case import ScheduleDataExportUseCase
from services.file_service.application.use_cases.export.validate_export_status_use_case import (
    ValidateIdleExportTasksUseCase,
)
from services.file_service.application.use_cases.upload_use_case.upload_use_case import UploadUseCase
from settings.app_config import settings
from settings.app_secrets import secrets


def get_search_service_transient() -> DocumentSearchService:
    return OSDocumentSearchService(
        client=OpenSearchClient(
            client=AsyncOpenSearch(hosts=settings.OS_HOSTS, timeout=60, max_retries=5, retry_on_timeout=True)
        )
    )


def get_export_repository_transient() -> ExportTaskRepository:
    return SqlAlchExportTaskRepository(
        session_maker=async_sessionmaker(
            bind=create_async_engine(
                url=URL.create(
                    drivername="postgresql+asyncpg",
                    username=secrets.PG_USER,
                    password=secrets.PG_PASSWORD,
                    database=settings.PG_NAME,
                    host=settings.PG_HOST,
                    port=settings.PG_PORT,
                )
            ),
            expire_on_commit=False,
            class_=AsyncSession,
        )
    )


def get_async_message_broker_transient() -> AsyncMessageBrokerClient:
    return AsyncSNSWrapper(session=Session())


def get_message_broker_transient() -> MessageBrokerClient:
    return SNSWrapper(sns_client=AWSServiceProvider.get_sns_client(), sns_service=AWSServiceProvider.get_sns_service())


def get_object_storage_service_transient() -> ObjectStorageService:
    return BlobStorageService(
        blob_service_client=BlobServiceClient.from_connection_string(secrets.ASSET_STORAGE_CREDENTIALS)
    )


def get_extension_run_repo_transient() -> ExtensionRunRepository:
    return OSExtensionRunRepository(
        client=AsyncOpenSearch(hosts=settings.OS_HOSTS, timeout=60, max_retries=5, retry_on_timeout=True)
    )


def get_extension_result_repo_transient() -> ExtensionResultRepository:
    return OSExtensionResultRepository(
        client=AsyncOpenSearch(hosts=settings.OS_HOSTS, timeout=60, max_retries=5, retry_on_timeout=True),
        extension_run_repository=OSExtensionRunRepository(
            client=AsyncOpenSearch(hosts=settings.OS_HOSTS, timeout=60, max_retries=5, retry_on_timeout=True)
        ),
    )


def get_extension_detail_repo_transient() -> ExtensionDetailRepository:
    return SqlAlchExtensionDetailRepository(
        session_maker=async_sessionmaker(
            bind=create_async_engine(
                url=URL.create(
                    drivername="postgresql+asyncpg",
                    username=secrets.PG_USER,
                    password=secrets.PG_PASSWORD,
                    database=settings.PG_NAME,
                    host=settings.PG_HOST,
                    port=settings.PG_PORT,
                )
            ),
            expire_on_commit=False,
            class_=AsyncSession,
        )
    )


class DependencyBootstrapper:
    def __init__(self):
        self._injector = Injector()

    @property
    def injector(self):
        return self._injector

    def get[T](self, interface: Type[T]) -> T:
        return self.injector.get(interface=interface)

    def _bind_singleton[T](self, interface: Type[T], to: T):
        self.injector.binder.bind(interface=interface, to=to)

    def build(self) -> Self:
        self._bind_infrastructure()
        self._bind_repositories()
        self._bind_services()
        self._bind_singleton(
            interface=WorkerOrchestrator,
            to=ProcessPoolOrchestrator(),
        )

        self._bind_use_cases()
        return self

    async def cleanup(self):
        await self.get(AsyncOpenSearch).close()

    def _bind_infrastructure(self):
        self._bind_singleton(
            interface=AsyncOpenSearch,
            to=AsyncOpenSearch(hosts=settings.OS_HOSTS, timeout=60, max_retries=5, retry_on_timeout=True),
        )
        self._bind_singleton(
            interface=OpenSearchClient, to=OpenSearchClient(client=self.get(interface=AsyncOpenSearch))
        )
        self._bind_singleton(interface=Session, to=Session())
        self._bind_singleton(
            interface=MessageBrokerClient,
            to=SNSWrapper(
                sns_client=AWSServiceProvider.get_sns_client(), sns_service=AWSServiceProvider.get_sns_service()
            ),
        )
        self._bind_singleton(interface=AsyncMessageBrokerClient, to=AsyncSNSWrapper(session=self.get(Session)))
        self._bind_singleton(
            interface=MessageQueueClient, to=SQSWrapper(sqs_service=AWSServiceProvider.get_sqs_service())
        )
        self._bind_singleton(
            interface=AsyncEngine,
            to=create_async_engine(
                url=URL.create(
                    drivername="postgresql+asyncpg",
                    username=secrets.PG_USER,
                    password=secrets.PG_PASSWORD,
                    database=settings.PG_NAME,
                    host=settings.PG_HOST,
                    port=settings.PG_PORT,
                )
            ),
        )
        self._bind_singleton(
            interface=async_sessionmaker,
            to=async_sessionmaker(bind=self.get(interface=AsyncEngine), expire_on_commit=False, class_=AsyncSession),
        )

    def _bind_repositories(self):
        self._bind_singleton(
            interface=DocumentSearchService, to=OSDocumentSearchService(client=self.get(interface=OpenSearchClient))
        )
        self._bind_singleton(
            interface=TemplateRepository,
            to=OSTemplateRepository(
                client=self.get(AsyncOpenSearch), search_service=self.get(interface=DocumentSearchService)
            ),
        )
        self._bind_singleton(
            interface=PlanRepository,
            to=OSPlanRepository(
                client=self.get(AsyncOpenSearch), search_service=self.get(interface=DocumentSearchService)
            ),
        )
        self._bind_singleton(
            interface=UseCaseRepository,
            to=OSUseCaseRepository(
                client=self.get(AsyncOpenSearch), search_service=self.get(interface=DocumentSearchService)
            ),
        )
        self._bind_singleton(
            interface=DeprEventRepository, to=OSDeprEventRepository(client=self.get(interface=OpenSearchClient))
        )
        self._bind_singleton(
            interface=EventRepository,
            to=OSEventRepository(
                client=self.get(interface=AsyncOpenSearch), search_service=self.get(DocumentSearchService)
            ),
        )
        self._bind_singleton(
            interface=EnvironmentRepository, to=OSEnvironmentRepository(client=self.get(interface=AsyncOpenSearch))
        )
        self._bind_singleton(
            interface=MemberUserRepository,
            to=SqlAlchMemberUserRepository(
                session_maker=self.get(interface=async_sessionmaker), engine=self.get(interface=AsyncEngine)
            ),
        )
        self._bind_singleton(
            interface=MemberUserOSTasksRepository,
            to=SqlAlchMemberUserOSTasksRepository(session_maker=self.get(interface=async_sessionmaker)),
        )
        self._bind_singleton(
            interface=MemberUserSettingsRepository,
            to=SqlAlchMemberUserSettingsRepository(
                session_maker=self.get(interface=async_sessionmaker), engine=self.get(interface=AsyncEngine)
            ),
        )
        self._bind_singleton(
            interface=ExportTaskRepository,
            to=SqlAlchExportTaskRepository(session_maker=self.get(interface=async_sessionmaker)),
        )
        self._bind_singleton(
            interface=ExtensionRunRepository, to=OSExtensionRunRepository(client=self.get(AsyncOpenSearch))
        )
        self._bind_singleton(
            interface=ExtensionResultRepository,
            to=OSExtensionResultRepository(
                client=self.get(AsyncOpenSearch), extension_run_repository=self.get(interface=ExtensionRunRepository)
            ),
        )
        self._bind_singleton(
            interface=ExtensionDetailRepository,
            to=SqlAlchExtensionDetailRepository(session_maker=self.get(interface=async_sessionmaker)),
        )

    def _bind_services(self):
        self._bind_singleton(
            interface=DeprJobService, to=DeprOpenSearchJobService(client=self.get(interface=AsyncOpenSearch))
        )
        self._bind_singleton(
            interface=ObjectStorageService,
            to=BlobStorageService(
                blob_service_client=BlobServiceClient.from_connection_string(secrets.ASSET_STORAGE_CREDENTIALS)
            ),
        )

    def _bind_use_cases(self):
        self._bind_singleton(
            interface=DeleteByFilterUseCase,
            to=DeleteByFilterUseCase(
                job_service=self.get(interface=DeprJobService),
                event_repo=self.get(interface=DeprEventRepository),
                os_tasks_repository=self.get(interface=MemberUserOSTasksRepository),
            ),
        )
        self._bind_singleton(
            interface=DeleteAllUserDataUseCase,
            to=DeleteAllUserDataUseCase(
                job_service=self.get(interface=DeprJobService),
                event_repo=self.get(interface=DeprEventRepository),
                user_repository=self.get(interface=MemberUserRepository),
                os_tasks_repository=self.get(interface=MemberUserOSTasksRepository),
            ),
        )
        self._bind_singleton(
            interface=UploadUseCase,
            to=UploadUseCase(
                job_service=self.get(interface=DeprJobService),
                message_broker_client=self.get(interface=AsyncMessageBrokerClient),
                os_tasks_repository=self.get(interface=MemberUserOSTasksRepository),
            ),
        )
        self._bind_singleton(
            interface=CancelFileUploadUseCase,
            to=CancelFileUploadUseCase(),
        )

        self._bind_singleton(
            interface=ScheduleDataExportUseCase,
            to=ScheduleDataExportUseCase(
                member_user_settings_repo=self.injector.get(MemberUserSettingsRepository),
                message_broker_client=self.injector.get(AsyncMessageBrokerClient),
                export_repo=self.injector.get(ExportTaskRepository),
            ),
        )

        self._bind_singleton(
            interface=ProcessDataExportUseCase,
            to=ProcessDataExportUseCase(
                search_service=self.injector.get(DocumentSearchService),
                object_storage_service=self.injector.get(ObjectStorageService),
                message_broker_client=self.injector.get(AsyncMessageBrokerClient),
                export_repo=self.injector.get(ExportTaskRepository),
                extension_run_repo=self.injector.get(ExtensionRunRepository),
                extension_result_repo=self.injector.get(ExtensionResultRepository),
                extension_detail_repo=self.injector.get(ExtensionDetailRepository),
            ),
        )
        self._bind_singleton(
            interface=ValidateIdleExportTasksUseCase,
            to=ValidateIdleExportTasksUseCase(
                export_repo=self.injector.get(ExportTaskRepository),
            ),
        )

        self._bind_singleton(
            interface=FetchExportUrlUseCase,
            to=FetchExportUrlUseCase(
                object_storage_service=self.injector.get(ObjectStorageService),
            ),
        )


bootstrapper = DependencyBootstrapper().build()
