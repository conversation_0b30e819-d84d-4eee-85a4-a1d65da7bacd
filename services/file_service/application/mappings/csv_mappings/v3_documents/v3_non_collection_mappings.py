from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.schemas.contact import ContactFields
from services.base.domain.schemas.events.activity import ActivityFields
from services.base.domain.schemas.events.event import EventFields
from services.base.domain.schemas.events.medication.medication import MedicationFields
from services.base.domain.schemas.events.note import NoteFields
from services.base.domain.schemas.events.person import PersonFields
from services.base.domain.schemas.events.plan import PlanFields
from services.base.domain.schemas.events.symptom import SymptomFields
from services.base.domain.schemas.templates.template import TemplateFields
from services.file_service.application.enums.exportable_data_type import ExportableType

V3CSVEventBaseMapping = [
    DocumentLabels.TIMESTAMP,
    EventFields.TYPE,
    EventFields.CATEGORY,
    EventFields.NAME,
    DocumentLabels.DURATION,
]

V3CSVMappings = {
    ExportableType.Activity: [
        *V3CSVEventBaseMapping,
        # Core Event Fields
        ActivityFields.RATING,
        ActivityFields.NOTE,
    ],
    ExportableType.Note: [
        *V3CSVEventBaseMapping,
        # Note Fields
        NoteFields.NOTE,
    ],
    ExportableType.Symptom: [
        *V3CSVEventBaseMapping,
        # Symptom Fields
        SymptomFields.RATING,
        SymptomFields.NOTE,
        SymptomFields.BODY_PARTS,
    ],
    ExportableType.Medication: [
        *V3CSVEventBaseMapping,
        # Symptom Fields
        MedicationFields.NOTE,
        MedicationFields.CONSUMED_AMOUNT,
        MedicationFields.CONSUME_UNIT,
        MedicationFields.MEDICATION_DETAILS,
        MedicationFields.SINGLE_DOSE_INFORMATION,
    ],
    ExportableType.Person: [
        *V3CSVEventBaseMapping,
        # Person Fields
        PersonFields.RATING,
        PersonFields.NOTE,
        PersonFields.CONTACT_ID,
    ],
}

V3NonEventCSVMappings = {
    ExportableType.EventTemplate: [
        EventFields.TYPE,
        EventFields.NAME,
        TemplateFields.DOCUMENT_NAME,
        TemplateFields.DOCUMENT_TYPE,
    ],
    ExportableType.GroupTemplate: [
        EventFields.TYPE,
        EventFields.NAME,
        TemplateFields.TEMPLATE_IDS,
    ],
    ExportableType.Plan: [
        EventFields.NAME,
        PlanFields.TEMPLATE_ID,
        PlanFields.STREAK,
        PlanFields.IS_URGENT,
        PlanFields.IS_CONFIRMATION_REQUIRED,
        PlanFields.PRIORITY,
        PlanFields.PROMPT,
        PlanFields.CURRENT_COMPLETED,
        PlanFields.NEXT_SCHEDULED_AT,
        PlanFields.FIRST_COMPLETED_AT,
        PlanFields.MAX_COMPLETED,
        PlanFields.NOTE,
    ],
    ExportableType.UseCase: [
        EventFields.NAME,
    ],
    ExportableType.Contact: [
        ContactFields.LAST_NAME,
        ContactFields.FIRST_NAME,
        ContactFields.COMPANY,
        ContactFields.ADDRESS,
        ContactFields.NOTE,
        ContactFields.BIRTHDAY,
        ContactFields.RELATIONSHIP,
    ],
}
