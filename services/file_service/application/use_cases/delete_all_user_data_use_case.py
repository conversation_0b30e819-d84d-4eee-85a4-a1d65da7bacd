import asyncio
import logging
from uuid import UUID

from services.base.application.database.depr_event_repository import DeprEventRepository
from services.base.application.database.depr_job_service import DeprJobService
from services.base.application.database.models.filter_types import UserUUIDTermsFilter
from services.base.application.database.models.filters import FilterBuilder, Filters
from services.base.application.exceptions import ConflictingActionInProgress, IncorrectOperationException
from services.base.application.utils.data_task_orchestration import DataTaskOrchestration
from services.base.domain.enums.metadata import Organization
from services.base.domain.enums.os_task_states import OSTaskStates
from services.base.domain.enums.os_task_types import OSTaskTypes
from services.base.domain.repository.member_user_os_tasks_repository import MemberUserOSTasksRepository
from services.base.domain.repository.member_user_repository import MemberUserRepository
from services.base.domain.schemas.member_user.member_user_os_tasks import MemberUserOSTasks
from services.base.infrastructure.database.opensearch.opensearch_index_mappings import get_os_index_models
from services.file_service.utils.cleanup import delete_all_user_storage_data


class DeleteAllUserDataUseCase:
    def __init__(
        self,
        event_repo: DeprEventRepository,
        job_service: DeprJobService,
        os_tasks_repository: MemberUserOSTasksRepository,
        user_repository: MemberUserRepository,
    ):
        self._job_service = job_service
        self._event_repo = event_repo
        self._os_tasks_repository = os_tasks_repository
        self._user_repository = user_repository

    async def execute_async(
        self,
        user_uuid: UUID,
        **kwargs,
    ):
        can_proceed = True
        for org in Organization:
            can_proceed = (
                await DataTaskOrchestration.can_delete_uploaded_user_data_in_db(
                    user_uuid=user_uuid,
                    job_service=self._job_service,
                    os_tasks_repository=self._os_tasks_repository,
                    organization=org,
                )
                and can_proceed
            )
        if not can_proceed:
            raise ConflictingActionInProgress(
                "Can't proceed with deletion - upload or another deletion are already in progress"
            )
        delete_all_user_storage_data(user_uuid=user_uuid)

        coroutines = []
        for index in get_os_index_models():
            coroutines.append(
                asyncio.create_task(self.delete_data_by_index_name(user_uuid=user_uuid, index_name=index.name))
            )

    async def delete_data_by_index_name(
        self,
        user_uuid: UUID,
        index_name: str,
    ):
        task_id = await self._event_repo.delete_by_query(
            user_uuid=user_uuid,
            data_schema=index_name,
            filters=Filters(must_filters=FilterBuilder(terms_filters=[UserUUIDTermsFilter(value=[str(user_uuid)])])),
        )
        user = await self._user_repository.get_by_uuid(user_uuid=user_uuid)
        # In certain cases, the user is deleted at this point and there is no need persisting the task
        if user:
            try:
                await self._os_tasks_repository.insert_or_update(
                    [
                        MemberUserOSTasks(
                            task_id=task_id,
                            user_uuid=user_uuid,
                            task_type=OSTaskTypes.DELETE_BY_QUERY,
                            state=OSTaskStates.SCHEDULED,
                        )
                    ]
                )
            except IncorrectOperationException as err:
                logging.warning(err.message)
