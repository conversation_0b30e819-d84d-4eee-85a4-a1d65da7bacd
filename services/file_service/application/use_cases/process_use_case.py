import logging
from zoneinfo import ZoneInfo

from services.base.application.exceptions import ActionCancelledException
from services.base.application.use_case_base import UseCaseBase
from services.base.domain.enums.upload_states import UploadStates
from services.base.infrastructure.database.sql_alchemy.db_state_manager import get_db_session
from services.base.infrastructure.database.sql_alchemy.models.upload_state_entity import UploadStateEntity
from services.file_service.application.event_models.extract_event_model import ExtractEventModel
from services.file_service.data_providers_loaders import DATA_PROVIDERS_LOADERS


class ProcessUseCase(UseCaseBase):
    def execute(self, input_object: ExtractEventModel, **kwargs):
        try:
            with get_db_session() as db_session:
                if not UploadStateEntity.update_state_if_eligible(
                    db_session,
                    user_uuid=input_object.user_uuid,
                    provider=input_object.provider,
                    state=UploadStates.LOADING,
                ):
                    raise ActionCancelledException(message="Action was cancelled externally.")

            # RUN THE LOADERS
            logging.info(f"processing provider: {input_object.provider}")
            DATA_PROVIDERS_LOADERS[input_object.provider](
                user_uuid=input_object.user_uuid,
                data_dir_path=input_object.dir_path,
                fallback_timezone=ZoneInfo(input_object.fallback_timezone),
            )

            with get_db_session() as db_session:
                if not UploadStateEntity.update_state_if_eligible(
                    db_session,
                    user_uuid=input_object.user_uuid,
                    provider=input_object.provider,
                    state=UploadStates.FINISHED,
                ):
                    raise ActionCancelledException(message="Action was cancelled externally.")

        except ActionCancelledException as error:
            raise error

        except Exception as error:
            with get_db_session() as db_session:
                UploadStateEntity.invalidate_loading(
                    db_session,
                    user_uuid=input_object.user_uuid,
                    provider=input_object.provider,
                    state=UploadStates.FAILED,
                )

            raise error
