"""FITBIT FILE LOADERS RUNNER"""

from uuid import UUID
from zoneinfo import ZoneInfo

from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.provider import SupportedDataProviders
from services.file_service.application.loaders.file_loader_run_info import FileLoaderRunInfo
from services.file_service.application.loaders.file_loader_runner_base import FileLoadersRunnerBase

from .file_loaders.heart_rate_loader import HeartRateLoader
from .file_loaders.resting_heart_rate_loader import RestingHeartRateLoader
from .file_loaders.sleep_detail_loader import SleepDetailLoader
from .file_loaders.weight_loader import WeightLoader


class FitbitFileLoadersRunner(FileLoadersRunnerBase):
    """Runs all current FITBIT file loaders"""

    provider: SupportedDataProviders = SupportedDataProviders.FITBIT

    def __init__(
        self,
        user_uuid: UUID,
        data_dir_path: str,
        fallback_timezone: ZoneInfo,
    ):
        super().__init__(user_uuid=user_uuid, data_dir_path=data_dir_path, fallback_timezone=fallback_timezone)

        self.run_loaders(
            [
                FileLoaderRunInfo(loader=WeightLoader, data_types=[DataType.BodyMetric]),
                FileLoaderRunInfo(loader=RestingHeartRateLoader, data_types=[DataType.RestingHeartRate]),
                FileLoaderRunInfo(loader=SleepDetailLoader, data_types=[DataType.Sleep]),
                FileLoaderRunInfo(loader=HeartRateLoader, data_types=[DataType.HeartRate]),
            ]
        )
