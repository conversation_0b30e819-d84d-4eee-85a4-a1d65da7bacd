"""AMAZON FILE LOADERS RUNNER"""

from uuid import UUID
from zoneinfo import ZoneInfo

from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.provider import SupportedDataProviders
from services.file_service.application.loaders.file_loader_run_info import FileLoaderRunInfo
from services.file_service.application.loaders.file_loader_runner_base import FileLoadersRunnerBase

from .file_loaders.shopping_loader import ShoppingLoader


class AmazonFileLoadersRunner(FileLoadersRunnerBase):
    """Runs all current AMAZON file loaders"""

    provider: SupportedDataProviders = SupportedDataProviders.AMAZON

    def __init__(self, user_uuid: UUID, data_dir_path: str, fallback_timezone: ZoneInfo):
        super().__init__(user_uuid=user_uuid, data_dir_path=data_dir_path, fallback_timezone=fallback_timezone)

        self.run_loaders(
            [
                FileLoaderRunInfo(
                    loader=ShoppingLoader,
                    data_types=[DataType.ShoppingActivity],
                ),
            ]
        )
