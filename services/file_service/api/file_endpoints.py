import logging
from typing import List
from uuid import UUI<PERSON>
from zoneinfo import ZoneInfo

from fastapi import APIRouter, Depends, File, UploadFile
from fastapi_injector import Injected
from starlette import status

from services.base.api.authentication.auth_guard import get_current_uuid
from services.base.api.message_response import SingleMessageResponse
from services.base.api.timezone import get_fallback_timezone
from services.base.application.exceptions import RuntimeException
from services.base.domain.enums.provider import SupportedDataProviders
from services.file_service.api.constants import FileEndpointRoutes, FileServicePrefixes
from services.file_service.application.use_cases.cancel_file_upload_use_case import CancelFileUploadUseCase
from services.file_service.application.use_cases.delete_all_user_data_use_case import (
    DeleteAllUserDataUseCase,
)
from services.file_service.application.use_cases.upload_use_case.exceptions import UploadUseCaseError
from services.file_service.application.use_cases.upload_use_case.upload_use_case import UploadUseCase
from services.file_service.application.use_cases.upload_use_case.upload_use_case_input import UploadUseCaseInput

file_router = APIRouter(
    prefix=f"{FileServicePrefixes.VERSION2_PREFIX}",
    tags=[],
    responses={404: {"description": "Not found"}},
)

TAG_GENERAL = "general"
TAG_FILE_UPLOADING = "file uploading"
TAG_DATA_DELETE = "data delete"


@file_router.get(
    FileEndpointRoutes.SUPPORTED_DATA_PROVIDERS,
    response_model=List[SupportedDataProviders],
    tags=[TAG_GENERAL],
    dependencies=[Depends(get_current_uuid)],
)
def supported_data_providers_endpoint():
    return [provider.value for provider in SupportedDataProviders]


@file_router.post(
    FileEndpointRoutes.UPLOAD_PROVIDER_FILE, response_model=SingleMessageResponse, tags=[TAG_FILE_UPLOADING]
)
async def upload_provider_file_endpoint(
    provider: SupportedDataProviders,
    default_timezone: ZoneInfo = Depends(get_fallback_timezone),
    user_uuid: UUID = Depends(get_current_uuid),
    use_case: UploadUseCase = Injected(UploadUseCase),
    zip_file: UploadFile = File(...),
):
    try:
        return SingleMessageResponse(
            message=await use_case.execute_async(
                user_uuid=user_uuid,
                input_object=UploadUseCaseInput(provider=provider, file=zip_file),
                default_timezone=default_timezone,
            )
        )
    except UploadUseCaseError as error:
        logging.exception(f"Unable to upload file, user: {user_uuid}, err: {error}")
        raise RuntimeException(message="Unable to upload file. Please try again later.") from error


@file_router.delete(
    FileEndpointRoutes.DELETE_ALL_DATA,
    response_model=SingleMessageResponse,
    status_code=status.HTTP_202_ACCEPTED,
    tags=[TAG_DATA_DELETE],
)
async def delete_all_user_data_endpoint(
    current_uuid: UUID = Depends(get_current_uuid),
    use_case: DeleteAllUserDataUseCase = Injected(DeleteAllUserDataUseCase),
):
    await use_case.execute_async(user_uuid=current_uuid)
    return SingleMessageResponse(message="Successfully scheduled deletion")


@file_router.post(
    FileEndpointRoutes.CANCEL_FILE_UPLOAD,
    response_model=SingleMessageResponse,
    status_code=status.HTTP_202_ACCEPTED,
)
def cancel_file_upload_endpoint(
    provider: SupportedDataProviders,
    current_uuid: UUID = Depends(get_current_uuid),
    use_case: CancelFileUploadUseCase = Injected(CancelFileUploadUseCase),
):
    use_case.execute(user_uuid=current_uuid, provider=provider)

    return SingleMessageResponse(message="File upload cancelled.")
