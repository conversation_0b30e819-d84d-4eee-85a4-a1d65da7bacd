from uuid import UUID

from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.enums.provider import SupportedDataProviders
from services.base.tasks import futures_dict, futures_dict_lock
from services.base.tasks.task_cancellation import cancel_task


def cancel_concurrent_file_tasks(user_uuid: UUID, provider: SupportedDataProviders) -> None:
    # TODO(jaja): make sure message queue is also emptied
    with futures_dict_lock:
        for _, future_wrapper in futures_dict.items():  # future_id, future_wrapper
            message_body = future_wrapper.message.message_body
            if (
                not future_wrapper.future.done()
                and user_uuid == message_body.get(DocumentLabels.USER_UUID, "")
                and provider.value == message_body.get(DocumentLabels.PROVIDER, "")
            ):
                cancel_task(future_wrapper)
