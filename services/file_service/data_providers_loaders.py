from services.base.domain.enums.provider import SupportedDataProviders
from services.file_service.application.loaders.amazon_loaders.amazon_file_loaders_runner import AmazonFileLoadersRunner
from services.file_service.application.loaders.facebook_loaders.facebook_file_loaders_runner import (
    FacebookFileLoadersRunner,
)
from services.file_service.application.loaders.fitbit_loaders.fitbit_file_loaders_runner import FitbitFileLoadersRunner
from services.file_service.application.loaders.google_loaders.google_file_loaders_runner import GoogleFileLoadersRunner
from services.file_service.application.loaders.netflix_loaders.netflix_file_loaders_runner import (
    NetflixFileLoadersRunner,
)

DATA_PROVIDERS_LOADERS = {
    SupportedDataProviders.AMAZON: AmazonFileLoadersRunner,
    SupportedDataProviders.FACEBOOK: FacebookFileLoadersRunner,
    SupportedDataProviders.FITBIT: FitbitFileLoadersRunner,
    SupportedDataProviders.GOOGLE: GoogleFileLoadersRunner,
    SupportedDataProviders.NETFLIX: NetflixFileLoadersRunner,
}
