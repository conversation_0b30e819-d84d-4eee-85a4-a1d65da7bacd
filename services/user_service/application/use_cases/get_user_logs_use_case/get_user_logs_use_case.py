from typing import List
from uuid import UUID

from services.base.application.use_case_base import UseCaseBase
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.metadata import Organization
from services.base.domain.schemas.metadata import MetadataFields
from services.base.domain.schemas.user_action_log import UserAction<PERSON>og<PERSON>ields
from services.base.infrastructure.database.opensearch.query_methods.data_fetchers import get_fields
from services.base.infrastructure.database.opensearch.query_methods.results_filters import (
    filter_results_from_hits,
)
from services.base.infrastructure.database.opensearch.wrappers.client import get_default_os_client
from services.user_service.application.use_cases.get_user_logs_use_case.get_user_logs_use_case_output import (
    UserActionLogOutput,
)


class GetUserLogs(UseCaseBase):
    def execute(self, user_uuid: UUID, **kwargs) -> List[UserActionLogOutput]:
        client = get_default_os_client()
        # Get user logs from OpenSearch
        requested_fields = [
            DocumentLabels.TIMESTAMP,
            UserActionLogFields.USER_ACTION,
            UserActionLogFields.FILENAME,
            UserActionLogFields.LOG_EVENTS,
            UserActionLogFields.METADATA,
        ]

        results = get_fields(user_uuid=user_uuid, client=client, data_type=DataType.UserLogs)

        # Define output models
        filtered_results = filter_results_from_hits(results, requested_fields)
        output_results = []
        for result in filtered_results:
            user_action_log: UserActionLogOutput = UserActionLogOutput(
                timestamp=result[DocumentLabels.TIMESTAMP],
                action=result[UserActionLogFields.USER_ACTION],
                provider=Organization(result[UserActionLogFields.METADATA][MetadataFields.ORGANIZATION]),
                filename=result[UserActionLogFields.FILENAME],
                events=result.get(UserActionLogFields.LOG_EVENTS, []),
            )
            output_results.append(user_action_log)

        return output_results
