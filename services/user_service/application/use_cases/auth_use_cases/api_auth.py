from abc import ABC, abstractmethod
from typing import Any, <PERSON><PERSON>, Tu<PERSON>, Union
from uuid import UUID

from services.base.application.constants import OAuth2Keys
from services.base.domain.enums.client_apps import ClientApps
from services.base.domain.enums.provider import Provider, SupportedApiProviders, SupportedLoginProviders
from services.base.domain.repository.member_user_oauth2_repository import MemberUserOAuth2Repository
from services.base.domain.schemas.shared import BaseDataModel
from services.user_service.application.use_cases.auth_use_cases.utils import get_oauth2_client


class ProviderOAuth2ApiAuthorizer(ABC):
    _scopes: BaseDataModel = None

    @abstractmethod
    def build_api_auth_url(
        self, redirect_uri: str, scope: str, client: ClientApps, state: str, provider: SupportedApiProviders
    ) -> str:
        """
        Builds a provider OAuth authorization URL based on provided parameters.
        params:
            scope: Should be a string of provider scope names separated by a whitespace
        """

    @abstractmethod
    async def update_oauth_access_data(
        self,
        response_body: Any,
        member_user_oauth2_repository: MemberUserOAuth2Repository,
        user_uuid: UUID,
        provider: SupportedApiProviders,
    ) -> str:
        """
        Updates refresh token user had previously saved or create new one.
        Returned string is the access token.
        params:
            response_body: providers OAuth Token endpoint code exchange response
        """

    @property
    def scopes(self):
        if self._scopes is None:
            raise NotImplementedError("Provider scopes must not be None.")
        return self._scopes

    @abstractmethod
    async def exchange_auth_code(self, params: dict, token_url: str, headers: Optional[dict] = None):
        """
        Generates provider OAuth access token and possibly other data by providing client and input data from client
        params:
            params: dictionary contains:
                code - authorization code acquired from the provider
                redirect_uri - must match the redirect_uri sent to provider to get code
                client - client type (default is the web client)
                provider - type of login provider
                state - must match the state sent to provider to get code
            token_url: url of token endpoint
            headers: headers dict
        returns:
            Response - from google
            DOCS (Google):
                https://developers.google.com/identity/protocols/oauth2/native-app#exchange-authorization-code
            DOCS (RFC): https://datatracker.ietf.org/doc/html/rfc6749#section-4.1.3
        raises:
            HTTPException: when client is not supported
        """
        pass

    @abstractmethod
    async def renew_access_token(
        self, refresh_token: str, user_uuid: UUID, oauth2_repo: MemberUserOAuth2Repository
    ) -> str:
        """
        Renew access token by users refresh token.
        Returned string is the access token.
        params:
            refresh_token: refresh OAuth Token
        """
        pass

    @staticmethod
    def prepare_token_endpoint_parameters(
        code: str,
        redirect_uri: str,
        client: ClientApps,
        provider: Union[SupportedLoginProviders, SupportedApiProviders],
        state: str = "",
    ) -> Tuple[dict, str]:
        """
        Get settings class for provider and return oauth2 params and token URL
        returns:
            TUPLE - (params, token_url)
        """

        settings_class = get_oauth2_client(client=client, provider=Provider(provider.value))
        settings_class.TOKEN_URL_PARAMS[OAuth2Keys.CODE] = code
        settings_class.TOKEN_URL_PARAMS[OAuth2Keys.REDIRECT_URI] = redirect_uri
        settings_class.TOKEN_URL_PARAMS[OAuth2Keys.STATE] = state

        return (settings_class.model_dump()["TOKEN_URL_PARAMS"], settings_class.TOKEN_URL)
