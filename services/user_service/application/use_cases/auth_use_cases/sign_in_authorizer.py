from abc import ABC, abstractmethod
from typing import Optional

from services.base.domain.enums.client_apps import ClientApps
from services.base.domain.enums.provider import SupportedLoginProviders
from services.base.domain.schemas.shared import BaseDataModel


class GetSignInParametersOutputBoundary(BaseDataModel):
    client_id: str
    redirect_uri: str
    scope: str
    response_type: str
    access_type: str
    state: Optional[str] = None
    nonce: Optional[str] = None


class SignInAuthorizer(ABC):
    @property
    @abstractmethod
    def provider(self) -> SupportedLoginProviders: ...

    @property
    @abstractmethod
    def auth_url(self) -> str: ...

    @property
    @abstractmethod
    def token_url(self) -> str: ...

    @abstractmethod
    def _get_oauth2_client_id(self, client: ClientApps) -> str: ...

    @abstractmethod
    def get_sign_in_parameters(
        self, client: ClientApps, state: str, nonce: Optional[str]
    ) -> GetSignInParametersOutputBoundary: ...
