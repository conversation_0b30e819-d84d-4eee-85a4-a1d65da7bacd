import logging
from typing import Di<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>
from uuid import U<PERSON><PERSON>

from httpx import AsyncClient, HTTPStatusError, RequestError

from services.base.application.async_message_broker_client import AsyncMessageBrokerClient
from services.base.application.constants import OAuth2Keys
from services.base.application.exceptions import RuntimeException
from services.base.application.utils.urls import join_as_url
from services.base.domain.enums.client_apps import ClientApps
from services.base.domain.enums.google_access_type import GoogleAccessType
from services.base.domain.enums.provider import Provider, SupportedApiProviders, SupportedLoginProviders
from services.base.domain.repository.login_google_repository import LoginGoogleRepository
from services.base.domain.repository.member_user_oauth2_repository import MemberUserOAuth2Repository
from services.base.domain.repository.member_user_repository import MemberUserRepository
from services.base.domain.repository.member_user_settings_repository import MemberUserSettingsRepository
from services.base.domain.schemas.member_user.member_user import MemberUser
from services.user_service.application.boundaries.auth_by_code_input import AuthByCodeInput
from services.user_service.application.use_cases.auth_use_cases.api_auth import ProviderOAuth2ApiAuthorizer
from services.user_service.application.use_cases.auth_use_cases.auth import ProviderOAuth2LoginAuthorizer
from services.user_service.application.use_cases.auth_use_cases.enums.sign_in_action_type import SignInActionType
from services.user_service.application.use_cases.auth_use_cases.providers.google.sign_in_utils import GoogleOAuth2Utils
from services.user_service.application.use_cases.auth_use_cases.utils import (
    get_common_oauth2_authorize_parameters,
    get_oauth2_authorize_url,
)
from services.user_service.domain.providers.google.constants import (
    GoogleIdTokenKeys,
    GoogleOAuthKeys,
    GoogleScopes,
)
from settings.app_config import settings


class GoogleOAuth2Authorizer(ProviderOAuth2ApiAuthorizer, ProviderOAuth2LoginAuthorizer):
    _scopes = GoogleScopes()

    def __init__(
        self,
        member_user_repository: MemberUserRepository,
        member_user_settings_repository: MemberUserSettingsRepository,
        login_repository: LoginGoogleRepository,
        message_broker_client: AsyncMessageBrokerClient,
    ):
        self._member_user_repository = member_user_repository
        self._member_user_settings_repository = member_user_settings_repository
        self._login_repository = login_repository
        self._message_broker_client = message_broker_client

    def build_login_auth_url(
        self, redirect_uri: str, client: ClientApps, provider: SupportedLoginProviders, state: str, nonce: Optional[str]
    ) -> str:
        params = get_common_oauth2_authorize_parameters(
            redirect_uri=redirect_uri, scope="email profile", client=client, provider=provider.value, state=state
        )
        params[GoogleOAuthKeys.ACCESS_TYPE.value] = GoogleAccessType.ONLINE.value

        return join_as_url(
            base_url=get_oauth2_authorize_url(client=client, provider=provider.value), query_params=params
        )

    def get_login_auth_parameters(
        self, client: ClientApps, provider: SupportedLoginProviders, state: str
    ) -> Dict[str, str]:
        params = get_common_oauth2_authorize_parameters(
            redirect_uri=settings.AFTER_LOGIN_REDIRECT_URL,
            scope="email profile",
            client=client,
            provider=provider.value,
            state=state,
        )

        params[GoogleOAuthKeys.ACCESS_TYPE.value] = GoogleAccessType.ONLINE.value

        return params

    def build_api_auth_url(
        self, redirect_uri: str, scope: str, client: ClientApps, provider: SupportedApiProviders, state: str
    ) -> str:
        params = get_common_oauth2_authorize_parameters(
            redirect_uri=redirect_uri, scope=scope, client=client, provider=provider.value, state=state
        )

        params[GoogleOAuthKeys.ACCESS_TYPE.value] = GoogleAccessType.OFFLINE.value
        params[GoogleOAuthKeys.PROMPT.value] = "consent"
        params[GoogleOAuthKeys.INCLUDE_GRANTED_SCOPES.value] = "true"

        return join_as_url(
            base_url=get_oauth2_authorize_url(client=client, provider=provider.value), query_params=params
        )

    async def register_or_login_user(
        self,
        client: ClientApps,
        response_body: AuthByCodeInput,
        provider: SupportedLoginProviders,
        user_uuid: Optional[UUID] = None,
    ) -> Tuple[MemberUser, SignInActionType]:
        # response from provider token endpoint, also checks client validity
        try:
            # get oauth2 params and token url for provider as Tuple
            params, token_url = super().prepare_token_endpoint_parameters(
                code=response_body.code,
                redirect_uri=response_body.redirect_uri,
                client=response_body.client,
                provider=provider,
                state=response_body.state,
            )

            raw_token_response = await self.exchange_auth_code(
                params=params,
                headers={},
                token_url=token_url,
            )

            token_response_json: str = raw_token_response.json()

            token_response: dict = dict(token_response_json)
            provider_id_token_jwt = str(token_response[OAuth2Keys.ID_TOKEN.value])

        except Exception as error:
            logging.exception(
                "Unable to process Google Oauth2 response:\n%s, exception: %s", response_body, repr(error)
            )
            raise RuntimeException(message="Unable to process Oauth2 Google response.")

        provider_user_info = await GoogleOAuth2Utils.validate_user(
            client=client, provider_id_token_jwt=provider_id_token_jwt, provider=Provider(provider.value)
        )

        return await GoogleOAuth2Utils.register_or_update_user(
            provider_user_info=provider_user_info,
            member_user_repository=self._member_user_repository,
            member_user_settings_repository=self._member_user_settings_repository,
            login_google_repository=self._login_repository,
            message_broker_client=self._message_broker_client,
            user_uuid=user_uuid,
        )

    async def update_oauth_access_data(
        self,
        response_body: AuthByCodeInput,
        user_uuid: UUID,
        provider: SupportedApiProviders,
        member_user_oauth2_repository: MemberUserOAuth2Repository,
    ) -> str:
        # get oauth2 params and token url for provider as Tuple

        params, token_url = super().prepare_token_endpoint_parameters(
            code=response_body.code,
            redirect_uri=response_body.redirect_uri,
            client=response_body.client,
            provider=provider,
            state=response_body.state,
        )

        raw_token_response = await self.exchange_auth_code(
            params=params,
            token_url=token_url,
        )
        token_response_json: str = raw_token_response.json()
        try:
            token_response = dict(token_response_json)
            provider_id_token_jwt = str(token_response[OAuth2Keys.ID_TOKEN.value])
            provider_refresh_token = str(token_response[OAuth2Keys.REFRESH_TOKEN.value])
            provider_access_token = str(token_response[OAuth2Keys.REFRESH_TOKEN.value])
            provider_scopes = str(token_response[OAuth2Keys.SCOPE.value])
        except Exception as error:
            logging.exception(
                "Unable to process provider response:\n%s, exception: %s", token_response_json, repr(error)
            )
            raise RuntimeException(message="Unable to process Oauth2 Google response.")

        provider_user_info = await GoogleOAuth2Utils.validate_user(
            client=response_body.client, provider_id_token_jwt=provider_id_token_jwt, provider=Provider(provider.value)
        )

        google_user_id = str(provider_user_info.get(GoogleIdTokenKeys.SUB.value, ""))
        await GoogleOAuth2Utils.update_user_in_database(
            google_user_id=google_user_id,
            user_uuid=user_uuid,
            user_data=token_response_json,
            provider_refresh_token=provider_refresh_token,
            provider_scopes=provider_scopes,
            member_user_oauth2_repository=member_user_oauth2_repository,
        )

        return provider_access_token

    async def renew_access_token(self, refresh_token: str, user_uuid: UUID, oauth2_repo: MemberUserOAuth2Repository):
        pass

    async def exchange_auth_code(self, params: dict, token_url: str, headers: Optional[dict] = None):
        async with AsyncClient() as client:
            try:
                auth_response = await client.post(
                    url=token_url,
                    params=params,
                    headers=headers,
                )
                auth_response.raise_for_status()
                return auth_response
            except (RequestError, HTTPStatusError) as exc:
                logging.exception(f"Error response {exc.response.status_code} while requesting {exc.request.url!r}.")
                raise RuntimeException(message="Unable to process exchange auth code.")
