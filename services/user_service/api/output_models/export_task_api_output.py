from uuid import UUID

from pydantic import Field

from services.base.domain.annotated_types import AssetId, SerializableAwareDatetime
from services.base.domain.enums.task_status import TaskStatus
from services.base.domain.schemas.events.document_base import SystemPropertiesSchema
from services.base.domain.schemas.export_task import ExportTaskSchemaFields


class ExportTaskApiOutput(SystemPropertiesSchema):
    id: UUID
    status: TaskStatus = Field(..., alias=ExportTaskSchemaFields.STATUS)
    completed_at: SerializableAwareDatetime | None = Field(default=None, alias=ExportTaskSchemaFields.COMPLETED_AT)
    takeout_name: AssetId = Field(..., alias=ExportTaskSchemaFields.TAKEOUT_NAME)
