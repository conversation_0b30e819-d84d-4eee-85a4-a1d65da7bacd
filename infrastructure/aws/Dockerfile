# syntax = docker/dockerfile:1.2
FROM python:3.13-slim-bookworm as aws_provisioner

RUN rm -f /etc/apt/apt.conf.d/docker-clean
RUN --mount=type=cache,sharing=locked,target=/var/cache/apt \
    --mount=type=cache,sharing=locked,target=/var/lib/apt \
    apt-get update && \
    apt-get dist-upgrade -yq && \
    apt-get install -yq \
    curl
RUN rm -rf /var/lib/apt/lists/*

ENV PYTHONUNBUFFERED 1
ENV PYTHONDONTWRITEBYTECODE 1
ENV PYTHONPATH /app_code
ENV BUILD_VERSION $BUILD_VERSION
ENV IS_CONTAINERIZED true

RUN curl -fsSL https://deb.nodesource.com/setup_20.x | bash - && apt-get install -yq nodejs
RUN npm install -g aws-cdk-local@2.19.2 aws-cdk@2.175.1

RUN --mount=type=cache,target=/root/.cache/pip pip install --upgrade uv
COPY infrastructure/aws/requirements.txt .
RUN --mount=type=cache,target=/root/.cache/pip uv pip install --system -r requirements.txt

RUN groupadd -g 9995 appuser && \
	useradd -mu 1000 -g appuser appuser

WORKDIR /app_code
COPY ./infrastructure ./infrastructure
COPY ./settings ./settings
COPY ./services/base/domain ./services/base/domain

RUN chown -R appuser: /app_code
USER appuser
