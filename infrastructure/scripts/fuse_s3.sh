#!/bin/bash -e
aws configure set default.region us-east-1
S3_URL=$(aws secretsmanager get-secret-value --secret-id prod/myllif/s3 | jq --raw-output ".SecretString" | jq --raw-output ".S3URL")
S3_PASSWORD=$(aws secretsmanager get-secret-value --secret-id prod/myllif/s3 | jq --raw-output ".SecretString" | jq --raw-output ".S3PASSWORD")

echo ${S3_URL}:${S3_PASSWORD} > /etc/passwd-s3fs
chmod 0600 /etc/passwd-s3fs
s3fs ${S3_BUCKET_NAME} ${S3_MOUNT_DIRECTORY}
